#!/bin/bash

# Environment setup script for knot-core-refactored

echo "🔧 Setting up environment for knot-core-refactored..."
echo ""

# Check if .env already exists
if [ -f .env ]; then
    echo "⚠️  .env file already exists. Creating backup..."
    cp .env .env.backup.$(date +%Y%m%d_%H%M%S)
fi

echo "📝 Please provide your API credentials:"
echo ""

# Etsy API Configuration
echo "🛍️  ETSY API Configuration:"
read -p "Enter your Etsy API Key: " ETSY_API_KEY
read -p "Enter your Etsy Secret Key: " ETSY_SECRET_KEY
read -p "Enter your Etsy Shop ID: " ETSY_SHOP_ID
read -p "Enter your Etsy Access Token: " ETSY_ACCESS_TOKEN
read -p "Enter your Etsy Refresh Token: " ETSY_REFRESH_TOKEN

echo ""

# Shopify API Configuration
echo "🛒 SHOPIFY API Configuration:"
read -p "Enter your Shopify Shop Domain (e.g., your-shop.myshopify.com): " SHOPIFY_SHOP_DOMAIN
read -p "Enter your Shopify Access Token: " SHOPIFY_ACCESS_TOKEN

echo ""
echo "💾 Creating .env file..."

# Create .env file with user inputs
cat > .env << EOF
# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=knot-core-refactored
APP_VERSION=2.0.0

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=knot_core
DB_SYNCHRONIZE=true
DB_LOGGING=true

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_TIME=3600
JWT_PRIVATE_KEY=your-private-key
JWT_PUBLIC_KEY=your-public-key

# Amazon API Configuration (EXPIRED - DISABLED)
AMAZON_ACCESS_KEY_ID=disabled
AMAZON_SECRET_ACCESS_KEY=disabled
AMAZON_REGION=us-east-1
AMAZON_MARKETPLACE_ID=disabled
AMAZON_SELLER_ID=disabled

# Etsy API Configuration
ETSY_API_KEY=${ETSY_API_KEY}
ETSY_SECRET_KEY=${ETSY_SECRET_KEY}
ETSY_SHOP_ID=${ETSY_SHOP_ID}
ETSY_ACCESS_TOKEN=${ETSY_ACCESS_TOKEN}
ETSY_REFRESH_TOKEN=${ETSY_REFRESH_TOKEN}

# Shopify API Configuration
SHOPIFY_SHOP_DOMAIN=${SHOPIFY_SHOP_DOMAIN}
SHOPIFY_ACCESS_TOKEN=${SHOPIFY_ACCESS_TOKEN}
SHOPIFY_API_VERSION=2023-10

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Development Tools
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=8080
REDIS_COMMANDER_PORT=8081

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Queue Configuration
QUEUE_REDIS_HOST=redis
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=redis123
QUEUE_REDIS_DB=1

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
EOF

echo "✅ .env file created successfully!"
echo ""
echo "🚀 Next steps:"
echo "   1. Review the .env file and adjust any settings if needed"
echo "   2. Run: ./scripts/start-dev.sh"
echo "   3. Test the API at: http://localhost:3000/api"
echo ""
echo "📋 Quick test commands after startup:"
echo "   • Health check: curl http://localhost:3000/api/health"
echo "   • Sync orders: curl -X POST http://localhost:3000/api/platform/sync/last-week-orders"
echo "   • Get orders: curl http://localhost:3000/api/orders"
echo ""
