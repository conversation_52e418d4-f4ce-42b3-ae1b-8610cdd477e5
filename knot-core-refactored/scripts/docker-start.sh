#!/bin/bash

# Docker startup script for Knot Core API
set -e

echo "🐳 Starting Knot Core API with Docker..."

# Check if .env.docker exists, if not copy from example
if [ ! -f .env.docker ]; then
    echo "📝 Creating .env.docker from template..."
    cp .env.docker .env.docker
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start services
echo "🚀 Building and starting services..."
docker-compose up --build -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check service health
echo "🏥 Checking service health..."
docker-compose ps

# Show logs
echo "📋 Showing application logs..."
docker-compose logs -f app

echo "✅ Knot Core API is running!"
echo "🌐 API: http://localhost:3001/api"
echo "📚 Swagger: http://localhost:3001/api/docs"
echo "🗄️ PgAdmin: http://localhost:8080"
echo "🔴 Redis Commander: http://localhost:8081"
