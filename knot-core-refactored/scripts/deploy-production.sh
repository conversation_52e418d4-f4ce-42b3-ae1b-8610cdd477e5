#!/bin/bash

# Production deployment script for Knot Core with secure token management
set -e

echo "🚀 Starting production deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found!"
    print_warning "Please create .env.production with your production configuration"
    print_warning "You can copy from .env.production.example and update the values"
    exit 1
fi

# Check if encryption key is set
if grep -q "CHANGE_THIS_TO_A_SECURE_32_BYTE_HEX_KEY_FOR_PRODUCTION" .env.production; then
    print_error "ENCRYPTION_KEY is not set in .env.production!"
    print_warning "Generate a secure encryption key with:"
    print_warning "node -e \"console.log(require('crypto').randomBytes(32).toString('hex'))\""
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

print_status "Building production images..."
docker-compose -f docker-compose.prod.yml build --no-cache

print_status "Starting production services..."
docker-compose -f docker-compose.prod.yml up -d

print_status "Waiting for services to be healthy..."
sleep 10

# Check if services are running
if docker-compose -f docker-compose.prod.yml ps | grep -q "Up"; then
    print_success "Services are starting up..."
else
    print_error "Failed to start services"
    docker-compose -f docker-compose.prod.yml logs
    exit 1
fi

# Wait for health checks
print_status "Waiting for health checks to pass..."
for i in {1..30}; do
    if docker-compose -f docker-compose.prod.yml ps | grep -q "healthy"; then
        print_success "Health checks passed!"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Health checks failed after 30 attempts"
        docker-compose -f docker-compose.prod.yml logs app
        exit 1
    fi
    sleep 2
    echo -n "."
done

print_success "Production deployment completed successfully!"

echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.prod.yml ps

echo ""
echo "🌐 Access URLs:"
echo "  - Application: https://your-domain.com"
echo "  - API Documentation: https://your-domain.com/api"
echo "  - Health Check: https://your-domain.com/health"
echo "  - Metrics: http://your-domain.com:9090 (Prometheus)"

echo ""
echo "🔧 Useful commands:"
echo "  - View logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "  - Stop services: docker-compose -f docker-compose.prod.yml down"
echo "  - Restart app: docker-compose -f docker-compose.prod.yml restart app"

echo ""
print_warning "Remember to:"
print_warning "1. Configure your domain DNS to point to this server"
print_warning "2. Set up SSL certificates in docker/nginx/ssl/"
print_warning "3. Configure your firewall to allow ports 80 and 443"
print_warning "4. Set up monitoring and log aggregation"
print_warning "5. Configure backup for PostgreSQL data"

print_success "Deployment complete! 🎉"
