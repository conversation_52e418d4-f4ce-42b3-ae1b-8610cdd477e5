#!/usr/bin/env node

/**
 * Health check script for Docker container
 * Checks application health, database connectivity, and token management system
 */

const http = require('http');
const process = require('process');

const HEALTH_CHECK_URL = 'http://localhost:3001/api/health';
const TIMEOUT = 5000; // 5 seconds

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, { timeout: TIMEOUT }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: response
          });
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.on('error', (error) => {
      reject(error);
    });
  });
}

async function healthCheck() {
  try {
    console.log('🔍 Performing health check...');
    
    const response = await makeRequest(HEALTH_CHECK_URL);
    
    if (response.statusCode !== 200) {
      throw new Error(`Health check failed with status ${response.statusCode}`);
    }
    
    const { data } = response;
    
    // Check basic application health
    if (data.status !== 'ok') {
      throw new Error(`Application status is not ok: ${data.status}`);
    }
    
    console.log('✅ Application is healthy');
    
    // Check database connectivity
    if (data.database && data.database.status === 'connected') {
      console.log('✅ Database is connected');
    } else {
      throw new Error('Database is not connected');
    }
    
    // Check Redis connectivity
    if (data.redis && data.redis.status === 'connected') {
      console.log('✅ Redis is connected');
    } else {
      console.log('⚠️  Redis status unknown or not connected');
    }
    
    // Check token management system
    if (data.tokenManager && data.tokenManager.status === 'operational') {
      console.log('✅ Token management system is operational');
    } else {
      console.log('⚠️  Token management system status unknown');
    }
    
    console.log('🎉 All health checks passed');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    process.exit(1);
  }
}

// Run health check
healthCheck();
