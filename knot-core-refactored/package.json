{"name": "knot-core-refactored", "version": "2.0.0", "description": "Modern e-commerce platform integration system with clean architecture", "author": "Knot Core Team", "private": true, "license": "MIT", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "scripts": {"build": "nest build", "build:prod": "nest build --webpack", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d src/infrastructure/database/data-source.ts", "migration:run": "npm run typeorm -- migration:run -d src/infrastructure/database/data-source.ts", "migration:revert": "npm run typeorm -- migration:revert -d src/infrastructure/database/data-source.ts", "schema:drop": "npm run typeorm -- schema:drop -d src/infrastructure/database/data-source.ts", "seed": "ts-node src/infrastructure/database/seeds/run-seeds.ts", "docker:build": "docker build -t knot-core-refactored .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "prepare": "husky install", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\""}, "dependencies": {"@nestjs/axios": "^3.1.3", "@nestjs/bull": "^10.2.3", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/cqrs": "^10.2.8", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.1", "@nestjs/terminus": "^10.2.3", "@nestjs/throttler": "^6.3.0", "@nestjs/typeorm": "^10.0.2", "@types/passport-local": "^1.0.38", "axios": "^1.7.9", "axios-retry": "^4.5.0", "bcrypt": "^5.1.1", "bull": "^4.16.5", "cache-manager": "^5.7.6", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "etsy-ts": "^3.12.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "ioredis": "^5.4.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment-timezone": "^0.5.46", "nestjs-pino": "^4.1.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.13.1", "pino": "^9.5.0", "pino-http": "^10.3.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "resend": "^4.6.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-transactional": "^0.5.0", "uuid": "^11.0.5", "winston": "^3.17.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.1.4", "@nestjs/testing": "^10.4.15", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.14", "@types/node": "^22.10.6", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^@domain/(.*)$": "<rootDir>/domain/$1", "^@application/(.*)$": "<rootDir>/application/$1", "^@infrastructure/(.*)$": "<rootDir>/infrastructure/$1", "^@presentation/(.*)$": "<rootDir>/presentation/$1", "^@shared/(.*)$": "<rootDir>/shared/$1"}}, "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}}