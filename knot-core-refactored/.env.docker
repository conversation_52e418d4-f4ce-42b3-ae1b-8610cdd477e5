# Application Configuration
NODE_ENV=development
PORT=3001
BASE_URL=http://localhost:3001

# Database Configuration (Docker)
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=knot_core

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-2024
JWT_EXPIRATION_TIME=1h

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis123

# Shopify Configuration
SHOPIFY_SHOP_DOMAIN=knotheoryrings.myshopify.com
SHOPIFY_ACCESS_TOKEN=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

# Etsy Configuration
ETSY_API_KEY=8lky9udgc981qh7h1vqipih6
ETSY_SHOP_ID=6507168
ETSY_REFRESH_TOKEN=13208796.mBj8aG_josUWhI3rKsBnN_OSK-zsakJEfKGG6ryTbGwQDjuFiuKBUy1bSkO2dXUSa-gDWEaAP7CRBCPDOyJ6bkp1r0x

# Amazon Configuration (Optional)
AMAZON_ACCESS_KEY_ID=your-amazon-access-key
AMAZON_SECRET_ACCESS_KEY=your-amazon-secret-key
AMAZON_REGION=us-east-1

# PgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=8080

# Redis Commander Configuration
REDIS_COMMANDER_PORT=8081

# Email Configuration (Resend)
RESEND_API_KEY=re_9PMPvDud_69G4hUkg2M7Kpeh8D1zjD31M
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Knot Theory Rings

# Token Encryption Configuration
# Generate a new key with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
