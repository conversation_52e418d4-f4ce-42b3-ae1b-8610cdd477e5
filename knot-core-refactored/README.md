# Knot Theory Rings - Core API

A comprehensive e-commerce platform integration system for Knot Theory Rings, providing order management and multi-platform integration capabilities.

## 🚀 Features

- **🔐 JWT Authentication** - Secure user authentication and authorization
- **📦 Order Management** - Complete order processing with filtering and pagination
- **📊 Analytics** - Order statistics and reporting
- **🔗 Platform Integration** - Shopify, Etsy, and Amazon marketplace integration
- **🏥 Health Monitoring** - Application health checks and database seeding
- **📚 API Documentation** - Interactive Swagger/OpenAPI documentation
- **🐳 Docker Support** - Containerized deployment ready

## 🛠️ Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Documentation**: Swagger/OpenAPI
- **Validation**: Class Validator
- **Testing**: Jest
- **Containerization**: Docker & Docker Compose

## 📋 Prerequisites

- Node.js 18+ or Docker
- PostgreSQL 14+ (or use Docker Compose)
- npm or yarn

## 🚀 Quick Start

### Option 1: Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd knot-core-refactored
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start PostgreSQL** (if not using Docker)
   ```bash
   # Using Docker for database only
   docker run --name postgres-knot \
     -e POSTGRES_DB=knot_core \
     -e POSTGRES_USER=postgres \
     -e POSTGRES_PASSWORD=postgres \
     -p 5432:5432 -d postgres:14
   ```

5. **Run the application**
   ```bash
   npm run start:dev
   ```

### Option 2: Docker Development

1. **Clone and start with Docker Compose**
   ```bash
   git clone <repository-url>
   cd knot-core-refactored
   docker-compose up --build
   ```

## 🔧 Environment Configuration

Create a `.env` file with the following variables:

```env
# Application
NODE_ENV=development
PORT=3001

# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=knot_core

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-2024
JWT_EXPIRATION_TIME=1h

# Shopify Configuration
SHOPIFY_SHOP_DOMAIN=knotheoryrings.myshopify.com
SHOPIFY_ACCESS_TOKEN=your-shopify-token

# Etsy Configuration
ETSY_API_KEY=your-etsy-api-key
ETSY_SHOP_ID=your-shop-id
ETSY_REFRESH_TOKEN=your-etsy-refresh-token
```

## 📚 API Documentation

Once the application is running, access the interactive API documentation:

- **Swagger UI**: http://localhost:3001/api/docs
- **API Base URL**: http://localhost:3001/api/v1

### Authentication Flow

1. **Register a new user**:
   ```bash
   curl -X POST http://localhost:3001/api/v1/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "firstName": "John",
       "lastName": "Doe", 
       "email": "<EMAIL>",
       "password": "SecurePassword123!"
     }'
   ```

2. **Verify user email** (for testing):
   ```bash
   curl -X POST http://localhost:3001/api/v1/health/verify-user/<EMAIL>
   ```

3. **Login to get JWT token**:
   ```bash
   curl -X POST http://localhost:3001/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "SecurePassword123!"
     }'
   ```

4. **Use the token for authenticated requests**:
   ```bash
   curl -X GET http://localhost:3001/api/v1/orders \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

## 🗄️ Database Setup

The application automatically creates database tables on startup. To seed with sample data:

```bash
curl -X POST http://localhost:3001/api/v1/health/seed
```

This creates 4 sample orders with various statuses and platforms.

## 🔍 API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile

### Orders (Authenticated)
- `GET /orders` - List orders with filtering and pagination
- `GET /orders/stats` - Order statistics
- `GET /orders/recent` - Recent orders

### Health & Utilities
- `GET /health` - Application health check
- `POST /health/seed` - Seed database with sample data
- `POST /health/verify-user/:email` - Verify user email (testing)

## 🔍 Order Filtering

The orders endpoint supports comprehensive filtering:

```bash
# Filter by platform
GET /orders?platform=shopify

# Filter by status
GET /orders?status=pending

# Filter by customer email
GET /orders?customerEmail=<EMAIL>

# Filter by date range
GET /orders?startDate=2024-01-01&endDate=2024-12-31

# Filter by price range
GET /orders?minAmount=100&maxAmount=500

# Pagination
GET /orders?page=2&limit=10

# Combine filters
GET /orders?platform=etsy&status=shipped&page=1&limit=5
```

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run e2e tests
npm run test:e2e

# Run tests with coverage
npm run test:cov
```

## 🐳 Docker Deployment

### Development with Docker Compose

```bash
# Start all services
docker-compose up --build

# Start in background
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Docker Build

```bash
# Build production image
docker build -t knot-core-api .

# Run production container
docker run -p 3001:3001 --env-file .env knot-core-api
```

## 📁 Project Structure

```
src/
├── domain/           # Domain entities and business logic
├── infrastructure/   # Database, external services
├── presentation/     # Controllers, DTOs, guards
├── shared/          # Shared utilities, filters, interceptors
└── main.ts          # Application entry point
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.
