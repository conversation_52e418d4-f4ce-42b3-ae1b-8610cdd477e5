# 🚀 Knot Core API - Complete Guide

## 📋 Quick Start

### 1. **Import Postman Collection**
- Import `postman-collection.json` into Postman
- The collection includes all endpoints with examples
- JWT authentication is automatically handled

### 2. **Access Swagger UI**
- Open: http://localhost:3002/api/docs
- Interactive API documentation with live testing

### 3. **Application Status**
- API Base URL: `http://localhost:3002/api/v1`
- Swagger Docs: `http://localhost:3002/api/docs`
- Current Status: ✅ Running with 1,340 Shopify orders synced

---

## 🛒 **How to Get ALL Shopify Orders**

### **Method 1: Using Postman Collection**

1. **Import Collection**: Load `postman-collection.json`
2. **Login**: Use "🔐 Authentication > Login" request
3. **Sync All Orders**: Use "🛒 Shopify Sync > Sync ALL Shopify Orders"

### **Method 2: Direct API Call**

```bash
# 1. Login and get JWT token
curl -X POST http://localhost:3002/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}'

# 2. Sync ALL Shopify orders (replace YOUR_JWT_TOKEN)
curl -X POST "http://localhost:3002/api/v1/platform/sync/orders/shopify?startDate=2020-01-01T00:00:00Z&endDate=2025-12-31T23:59:59Z&limit=5000&forceUpdate=false" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Method 3: Using Swagger UI**

1. Open: http://localhost:3002/api/docs
2. Click "Authorize" and enter JWT token
3. Navigate to "Platform" section
4. Use `/platform/sync/orders/{platform}` endpoint

---

## 📊 **Current Database Status**

```json
{
  "totalOrders": 1340,
  "totalRevenue": "$71,416.76",
  "platforms": {
    "shopify": 1340,
    "etsy": 0 (token expired),
    "amazon": 0 (disabled)
  },
  "averageOrderValue": "$53.29"
}
```

---

## 🔧 **Key API Endpoints**

### **Authentication**
- `POST /auth/login` - Get JWT token
- `POST /auth/register` - Create new user
- `GET /auth/profile` - Get user profile

### **Platform Status**
- `GET /platform/status` - Check all platforms
- `GET /platform/status/shopify` - Shopify status
- `GET /platform/status/etsy` - Etsy status

### **Shopify Sync**
- `POST /platform/sync/orders/shopify` - Sync Shopify orders
- `POST /platform/sync/shopify/products` - Sync Shopify products

### **Orders Management**
- `GET /orders` - Get all orders with filters
- `GET /orders/stats` - Order statistics
- `GET /orders/recent` - Recent orders

### **Advanced Operations**
- `POST /platform/sync/orders/all` - Sync all platforms
- `POST /platform/sync/last-week-orders` - Last week sync

---

## 🎯 **Sync Parameters**

### **Date Range Parameters**
- `startDate`: ISO 8601 format (e.g., `2020-01-01T00:00:00Z`)
- `endDate`: ISO 8601 format (e.g., `2025-12-31T23:59:59Z`)
- `limit`: Maximum orders to sync (recommended: 1000-5000)
- `forceUpdate`: Update existing orders (true/false)

### **Recommended Sync Strategies**

#### **Full Historical Sync**
```
startDate: 2020-01-01T00:00:00Z
endDate: 2025-12-31T23:59:59Z
limit: 5000
forceUpdate: false
```

#### **Recent Orders Only**
```
startDate: 2025-08-01T00:00:00Z
endDate: 2025-08-05T23:59:59Z
limit: 100
forceUpdate: false
```

#### **Force Update Existing**
```
startDate: 2025-08-01T00:00:00Z
endDate: 2025-08-05T23:59:59Z
limit: 100
forceUpdate: true
```

---

## 🔍 **Order Filtering & Querying**

### **Filter by Platform**
```bash
GET /orders?source=shopify&limit=100
GET /orders?source=etsy&limit=100
```

### **Filter by Date Range (externalCreatedAt)**
```bash
GET /orders?startDate=2025-08-01T00:00:00Z&endDate=2025-08-05T23:59:59Z&limit=100
```
**Note**: Date filtering uses `externalCreatedAt` (order creation date on the platform), not internal database timestamps.

### **Sorting Options**
```bash
GET /orders?sortBy=externalCreatedAt&sortOrder=desc&limit=50
GET /orders?sortBy=totalPrice&sortOrder=asc&limit=50
```

### **Pagination**
```bash
GET /orders?page=1&limit=50
GET /orders?page=2&limit=50
```

---

## ⚡ **Performance Tips**

1. **Batch Processing**: Use limit=1000-5000 for bulk syncs
2. **Incremental Sync**: Sync recent orders frequently
3. **Avoid Force Updates**: Only use when necessary
4. **Monitor Logs**: Check application logs for errors
5. **Rate Limiting**: Shopify API has rate limits

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Etsy Token Expired**
- **Issue**: Etsy shows "disconnected"
- **Solution**: Refresh token via `/platform/tokens/etsy/refresh`

#### **No Orders Returned**
- **Issue**: Sync returns 0 orders
- **Solution**: Check date range and platform status

#### **Authentication Failed**
- **Issue**: 401 Unauthorized
- **Solution**: Login again to get fresh JWT token

### **Error Codes**
- `200`: Success
- `401`: Authentication required
- `403`: Insufficient permissions
- `404`: Resource not found
- `500`: Server error

---

## 📈 **Monitoring & Analytics**

### **Key Metrics Endpoints**
- `GET /orders/stats` - Order statistics
- `GET /platform/statistics` - Platform metrics
- `GET /platform/history` - Sync history

### **Health Checks**
- `GET /health` - Application health
- `GET /health/ready` - Readiness check
- `GET /health/live` - Liveness check

---

## 🔐 **Security Notes**

1. **JWT Tokens**: Expire after 1 hour
2. **API Keys**: Stored in environment variables
3. **Rate Limiting**: Implemented for external APIs
4. **HTTPS**: Use in production
5. **CORS**: Configured for frontend access

---

## 📞 **Support**

- **Swagger UI**: http://localhost:3002/api/docs
- **Postman Collection**: `postman-collection.json`
- **Application Logs**: Check terminal output
- **Database**: PostgreSQL with 1,340+ orders
