# Production Environment Configuration
NODE_ENV=production
PORT=3001
BASE_URL=https://your-domain.com

# Database Configuration (Production)
DATABASE_HOST=your-production-db-host
DATABASE_PORT=5432
DATABASE_USERNAME=your-db-username
DATABASE_PASSWORD=your-secure-db-password
DATABASE_NAME=knot_core_prod
DATABASE_SSL=true

# JWT Configuration (Production)
JWT_SECRET=your-super-secure-jwt-secret-key-for-production-change-this
JWT_EXPIRATION_TIME=1h

# Redis Configuration (Production)
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-secure-redis-password

# Token Encryption Configuration (CRITICAL - Generate new key for production)
# Generate with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
ENCRYPTION_KEY=CHANGE_THIS_TO_A_SECURE_32_BYTE_HEX_KEY_FOR_PRODUCTION

# Shopify Configuration (Production)
SHOPIFY_SHOP_DOMAIN=your-shop.myshopify.com
SHOPIFY_ACCESS_TOKEN=your-production-shopify-token

# Etsy Configuration (Production)
ETSY_API_KEY=your-production-etsy-api-key
ETSY_SHOP_ID=your-etsy-shop-id

# Amazon Configuration (Production)
AMAZON_ACCESS_KEY_ID=your-production-amazon-access-key
AMAZON_SECRET_ACCESS_KEY=your-production-amazon-secret-key
AMAZON_REGION=us-east-1

# Email Configuration (Production)
RESEND_API_KEY=your-production-resend-api-key
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Company Name

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090

# Security Configuration
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Database Configuration
DB_SYNCHRONIZE=false
DB_LOGGING=false
DB_MIGRATIONS_RUN=true
