import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export interface AppConfig {
  nodeEnv: string;
  port: number;
  name: string;
  version: string;
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
}

export const appConfig = registerAs('app', (): AppConfig => {
  const config = {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
    name: process.env.APP_NAME || 'knot-core-refactored',
    version: process.env.APP_VERSION || '2.0.0',
  };

  // Validate configuration
  const schema = Joi.object({
    nodeEnv: Joi.string().valid('development', 'production', 'test').required(),
    port: Joi.number().port().required(),
    name: Joi.string().required(),
    version: Joi.string().required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`App configuration validation error: ${error.message}`);
  }

  return {
    ...config,
    isDevelopment: config.nodeEnv === 'development',
    isProduction: config.nodeEnv === 'production',
    isTest: config.nodeEnv === 'test',
  };
});
