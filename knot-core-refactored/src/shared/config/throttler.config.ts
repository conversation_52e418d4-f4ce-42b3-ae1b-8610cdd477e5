import { registerAs } from '@nestjs/config';
import { ThrottlerModuleOptions } from '@nestjs/throttler';
import * as Jo<PERSON> from 'joi';

export interface ThrottlerConfig {
  ttl: number;
  limit: number;
}

export const throttlerConfig = registerAs('throttler', (): ThrottlerModuleOptions => {
  const config = {
    ttl: parseInt(process.env.RATE_LIMIT_TTL || '60', 10), // 60 seconds
    limit: parseInt(process.env.RATE_LIMIT_LIMIT || '100', 10), // 100 requests per TTL
  };

  // Validate configuration
  const schema = Joi.object({
    ttl: Joi.number().min(1).required(),
    limit: Joi.number().min(1).required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Throttler configuration validation error: ${error.message}`);
  }

  return {
    throttlers: [
      {
        ttl: config.ttl,
        limit: config.limit,
      },
    ],
  };
});
