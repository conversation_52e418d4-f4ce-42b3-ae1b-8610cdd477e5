import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';

export interface EmailConfig {
  apiKey: string;
  from: string;
  fromName: string;
  baseUrl: string;
}

export const emailConfig = registerAs('email', (): EmailConfig => {
  const config = {
    apiKey: process.env.RESEND_API_KEY || '',
    from: process.env.EMAIL_FROM || '<EMAIL>',
    fromName: process.env.EMAIL_FROM_NAME || 'Knot Theory Rings',
    baseUrl: process.env.BASE_URL || 'http://localhost:3001',
  };

  // Validate configuration
  const schema = Joi.object({
    apiKey: Joi.string().required(),
    from: Joi.string().email().required(),
    fromName: Joi.string().required(),
    baseUrl: Joi.string().uri().required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Email configuration validation error: ${error.message}`);
  }

  return config;
});
