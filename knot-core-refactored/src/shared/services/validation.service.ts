import { Injectable } from '@nestjs/common';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';

@Injectable()
export class ValidationService {
  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  isValidPassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate SKU format
   */
  isValidSKU(sku: string): boolean {
    // SKU should be alphanumeric with hyphens and underscores allowed
    const skuRegex = /^[A-Za-z0-9_-]+$/;
    return skuRegex.test(sku) && sku.length >= 3 && sku.length <= 50;
  }

  /**
   * Validate URL format
   */
  isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate phone number (basic international format)
   */
  isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s-()]/g, ''));
  }

  /**
   * Validate postal code (flexible format)
   */
  isValidPostalCode(postalCode: string, country?: string): boolean {
    const patterns: Record<string, RegExp> = {
      US: /^\d{5}(-\d{4})?$/,
      CA: /^[A-Za-z]\d[A-Za-z] \d[A-Za-z]\d$/,
      UK: /^[A-Za-z]{1,2}\d[A-Za-z\d]? \d[A-Za-z]{2}$/,
      DE: /^\d{5}$/,
      FR: /^\d{5}$/,
    };

    if (country && patterns[country.toUpperCase()]) {
      return patterns[country.toUpperCase()].test(postalCode);
    }

    // Generic postal code validation
    return /^[A-Za-z0-9\s-]{3,10}$/.test(postalCode);
  }

  /**
   * Validate currency code (ISO 4217)
   */
  isValidCurrencyCode(code: string): boolean {
    const validCurrencies = [
      'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
      'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW',
    ];
    return validCurrencies.includes(code.toUpperCase());
  }

  /**
   * Validate price (positive number with up to 2 decimal places)
   */
  isValidPrice(price: number | string): boolean {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return !isNaN(numPrice) && numPrice >= 0 && /^\d+(\.\d{1,2})?$/.test(numPrice.toString());
  }

  /**
   * Validate quantity (positive integer)
   */
  isValidQuantity(quantity: number | string): boolean {
    const numQuantity = typeof quantity === 'string' ? parseInt(quantity, 10) : quantity;
    return Number.isInteger(numQuantity) && numQuantity >= 0;
  }

  /**
   * Validate object using class-validator
   */
  async validateObject<T extends object>(
    cls: new () => T,
    obj: any,
  ): Promise<{ isValid: boolean; errors: ValidationError[] }> {
    const instance = plainToClass(cls, obj);
    const errors = await validate(instance);

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Sanitize string input
   */
  sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/[^\w\s-_.@]/g, ''); // Keep only alphanumeric, spaces, and common symbols
  }

  /**
   * Validate and sanitize HTML content
   */
  sanitizeHTML(html: string): string {
    // Basic HTML sanitization - in production, use a library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  /**
   * Check if string contains only alphanumeric characters
   */
  isAlphanumeric(str: string): boolean {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  /**
   * Validate date range
   */
  isValidDateRange(startDate: Date, endDate: Date): boolean {
    return startDate instanceof Date && 
           endDate instanceof Date && 
           !isNaN(startDate.getTime()) && 
           !isNaN(endDate.getTime()) && 
           startDate <= endDate;
  }
}
