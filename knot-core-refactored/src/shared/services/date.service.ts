import { Injectable } from '@nestjs/common';
import { format, parseISO, startOfDay, endOfDay, subDays, addDays } from 'date-fns';
import { toZonedTime, fromZonedTime } from 'date-fns-tz';

@Injectable()
export class DateService {
  private readonly defaultTimezone = 'UTC';

  /**
   * Get current date in ISO format
   */
  now(): Date {
    return new Date();
  }

  /**
   * Get current date as ISO string
   */
  nowISOString(): string {
    return new Date().toISOString();
  }

  /**
   * Format date to string
   */
  format(date: Date | string, formatString: string = 'yyyy-MM-dd HH:mm:ss'): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, formatString);
  }

  /**
   * Parse ISO string to Date
   */
  parseISO(dateString: string): Date {
    return parseISO(dateString);
  }

  /**
   * Get start of day
   */
  startOfDay(date: Date | string): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return startOfDay(dateObj);
  }

  /**
   * Get end of day
   */
  endOfDay(date: Date | string): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return endOfDay(dateObj);
  }

  /**
   * Subtract days from date
   */
  subDays(date: Date | string, amount: number): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return subDays(dateObj, amount);
  }

  /**
   * Add days to date
   */
  addDays(date: Date | string, amount: number): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return addDays(dateObj, amount);
  }

  /**
   * Convert date to timezone
   */
  toTimezone(date: Date | string, timezone: string = this.defaultTimezone): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return toZonedTime(dateObj, timezone);
  }

  /**
   * Convert timezone date to UTC
   */
  fromTimezone(date: Date | string, timezone: string = this.defaultTimezone): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return fromZonedTime(dateObj, timezone);
  }

  /**
   * Check if date is valid
   */
  isValid(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }

  /**
   * Get date range for SQL queries
   */
  getDateRange(startDate: Date | string, endDate?: Date | string): { start: Date; end: Date } {
    const start = this.startOfDay(startDate);
    const end = endDate ? this.endOfDay(endDate) : this.endOfDay(startDate);

    return { start, end };
  }

  /**
   * Convert to SQL datetime format
   */
  toSQLDateTime(date: Date | string, isEndOfDay: boolean = false): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    const targetDate = isEndOfDay ? this.endOfDay(dateObj) : this.startOfDay(dateObj);
    return this.format(targetDate, 'yyyy-MM-dd HH:mm:ss');
  }
}
