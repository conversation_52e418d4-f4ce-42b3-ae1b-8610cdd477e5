import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OAuthToken } from '../domain/common/entities/oauth-token.entity';
import { OAuthTokenRepository } from '../infrastructure/database/repositories/oauth-token.repository';

import { DateService } from './services/date.service';
import { CryptoService } from './services/crypto.service';
import { ValidationService } from './services/validation.service';
import { TransformService } from './services/transform.service';
import { EncryptionService } from './services/encryption.service';
import { TokenManagerService } from './services/token-manager.service';

@Global()
@Module({
  imports: [ConfigModule, TypeOrmModule.forFeature([OAuthToken])],
  providers: [
    DateService,
    CryptoService,
    ValidationService,
    TransformService,
    EncryptionService,
    OAuthTokenRepository,
    TokenManagerService,
  ],
  exports: [
    DateService,
    CryptoService,
    ValidationService,
    TransformService,
    EncryptionService,
    TokenManagerService,
  ],
})
export class SharedModule {}
