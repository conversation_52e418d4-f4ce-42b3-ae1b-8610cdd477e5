import { Controller, Get, Query, UseGuards, ParseIntPipe, DefaultValuePipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { JwtAuthGuard } from '@presentation/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@presentation/auth/guards/roles.guard';
import { Roles } from '@presentation/auth/decorators/roles.decorator';
import { UserRole } from '@domain/user/user-role.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';

import { OrderService } from './order.service';

export interface OrderFilters {
  platform?: PlatformSource;
  status?: OrderStatus;
  startDate?: Date;
  endDate?: Date;
  customerEmail?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface OrderListResponse {
  orders: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: OrderFilters;
}

@ApiTags('Orders')
@Controller('orders')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get orders with pagination and filters' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20, max: 100)',
  })
  @ApiQuery({
    name: 'platform',
    required: false,
    enum: PlatformSource,
    description: 'Filter by platform',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    description: 'Filter by order status',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date (ISO string)',
  })
  @ApiQuery({
    name: 'customerEmail',
    required: false,
    type: String,
    description: 'Filter by customer email',
  })
  @ApiQuery({
    name: 'minAmount',
    required: false,
    type: Number,
    description: 'Minimum order amount',
  })
  @ApiQuery({
    name: 'maxAmount',
    required: false,
    type: Number,
    description: 'Maximum order amount',
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            orders: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  externalOrderId: { type: 'string' },
                  orderNumber: { type: 'string' },
                  status: { type: 'string', enum: Object.values(OrderStatus) },
                  source: { type: 'string', enum: Object.values(PlatformSource) },
                  customerName: { type: 'string' },
                  customerEmail: { type: 'string' },
                  totalPrice: { type: 'number' },
                  currency: { type: 'string' },
                  externalCreatedAt: { type: 'string', format: 'date-time' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                },
              },
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'number' },
                limit: { type: 'number' },
                total: { type: 'number' },
                totalPages: { type: 'number' },
                hasNext: { type: 'boolean' },
                hasPrev: { type: 'boolean' },
              },
            },
            filters: { type: 'object' },
          },
        },
      },
    },
  })
  async getOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('platform') platform?: PlatformSource,
    @Query('status') status?: OrderStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('customerEmail') customerEmail?: string,
    @Query('minAmount') minAmount?: number,
    @Query('maxAmount') maxAmount?: number,
  ): Promise<{ success: boolean; data: OrderListResponse }> {
    // Validate and sanitize inputs
    const validatedPage = Math.max(1, page);
    const validatedLimit = Math.min(100, Math.max(1, limit));

    const filters: OrderFilters = {};

    if (platform && Object.values(PlatformSource).includes(platform)) {
      filters.platform = platform;
    }

    if (status && Object.values(OrderStatus).includes(status)) {
      filters.status = status;
    }

    if (startDate) {
      const parsedStartDate = new Date(startDate);
      if (!isNaN(parsedStartDate.getTime())) {
        filters.startDate = parsedStartDate;
      }
    }

    if (endDate) {
      const parsedEndDate = new Date(endDate);
      if (!isNaN(parsedEndDate.getTime())) {
        filters.endDate = parsedEndDate;
      }
    }

    if (customerEmail) {
      filters.customerEmail = customerEmail.trim();
    }

    if (minAmount !== undefined && !isNaN(minAmount)) {
      filters.minAmount = Math.max(0, minAmount);
    }

    if (maxAmount !== undefined && !isNaN(maxAmount)) {
      filters.maxAmount = Math.max(0, maxAmount);
    }

    const pagination: PaginationParams = {
      page: validatedPage,
      limit: validatedLimit,
    };

    const result = await this.orderService.getOrdersWithPagination(pagination, filters);

    return {
      success: true,
      data: result,
    };
  }

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get order statistics' })
  @ApiResponse({
    status: 200,
    description: 'Order statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalOrders: { type: 'number' },
            totalRevenue: { type: 'number' },
            ordersByPlatform: { type: 'object' },
            ordersByStatus: { type: 'object' },
            recentOrders: { type: 'number' },
            averageOrderValue: { type: 'number' },
          },
        },
      },
    },
  })
  async getOrderStats(): Promise<{
    success: boolean;
    data: {
      totalOrders: number;
      totalRevenue: number;
      ordersByPlatform: Record<PlatformSource, number>;
      ordersByStatus: Record<OrderStatus, number>;
      recentOrders: number;
      averageOrderValue: number;
    };
  }> {
    const stats = await this.orderService.getOrderStatistics();

    return {
      success: true,
      data: stats,
    };
  }

  @Get('recent')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get recent orders from last week' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of recent orders (default: 50)',
  })
  @ApiResponse({
    status: 200,
    description: 'Recent orders retrieved successfully',
  })
  async getRecentOrders(
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number,
  ): Promise<{ success: boolean; data: any[] }> {
    const validatedLimit = Math.min(200, Math.max(1, limit));

    // Get orders from last week
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);

    const filters: OrderFilters = {
      startDate: lastWeek,
    };

    const pagination: PaginationParams = {
      page: 1,
      limit: validatedLimit,
    };

    const result = await this.orderService.getOrdersWithPagination(pagination, filters);

    return {
      success: true,
      data: result.orders,
    };
  }
}
