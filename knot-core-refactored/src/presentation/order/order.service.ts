import { Injectable, Logger } from '@nestjs/common';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';
import {
  Between,
  FindManyOptions,
  MoreThanOrEqual,
  LessThanOrEqual,
  Like,
  DataSource,
} from 'typeorm';
import { Order } from '@domain/order/order.entity';

export interface OrderFilters {
  platform?: PlatformSource;
  status?: OrderStatus;
  startDate?: Date;
  endDate?: Date;
  customerEmail?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface OrderListResponse {
  orders: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: OrderFilters;
}

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get orders with pagination and filters
   */
  async getOrdersWithPagination(
    pagination: PaginationParams,
    filters: OrderFilters = {},
  ): Promise<OrderListResponse> {
    this.logger.log(`Getting orders with pagination: ${JSON.stringify({ pagination, filters })}`);

    // Build query options
    const queryOptions: FindManyOptions = {
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
      skip: (pagination.page - 1) * pagination.limit,
      take: pagination.limit,
    };

    // Build where conditions
    const whereConditions: any = {};

    if (filters.platform) {
      whereConditions.source = filters.platform;
    }

    if (filters.status) {
      whereConditions.status = filters.status;
    }

    if (filters.customerEmail) {
      whereConditions.customerEmail = Like(`%${filters.customerEmail}%`);
    }

    if (filters.startDate && filters.endDate) {
      whereConditions.externalCreatedAt = Between(filters.startDate, filters.endDate);
    } else if (filters.startDate) {
      whereConditions.externalCreatedAt = MoreThanOrEqual(filters.startDate);
    } else if (filters.endDate) {
      whereConditions.externalCreatedAt = LessThanOrEqual(filters.endDate);
    }

    if (filters.minAmount && filters.maxAmount) {
      whereConditions.totalPrice = Between(filters.minAmount, filters.maxAmount);
    } else if (filters.minAmount) {
      whereConditions.totalPrice = MoreThanOrEqual(filters.minAmount);
    } else if (filters.maxAmount) {
      whereConditions.totalPrice = LessThanOrEqual(filters.maxAmount);
    }

    queryOptions.where = whereConditions;

    // Execute queries
    const orderRepo = this.dataSource.getRepository(Order);
    const [orders, total] = await orderRepo.findAndCount(queryOptions);

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / pagination.limit);
    const hasNext = pagination.page < totalPages;
    const hasPrevious = pagination.page > 1;

    return {
      orders: orders.map(order => this.formatOrderForResponse(order)),
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext,
        hasPrev: hasPrevious,
      },
      filters,
    };
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    ordersByPlatform: Record<PlatformSource, number>;
    ordersByStatus: Record<OrderStatus, number>;
    recentOrders: number;
    averageOrderValue: number;
  }> {
    this.logger.log('Getting order statistics');

    // Get total orders and revenue
    const orderRepo = this.dataSource.getRepository(Order);
    const totalOrders = await orderRepo.count();
    const revenueResult = await orderRepo
      .createQueryBuilder('order')
      .select('SUM(order.totalPrice)', 'totalRevenue')
      .getRawOne();
    const totalRevenue = parseFloat(revenueResult?.totalRevenue || '0');

    // Get orders by platform
    const platformStats = await orderRepo
      .createQueryBuilder('order')
      .select('order.source', 'platform')
      .addSelect('COUNT(*)', 'count')
      .groupBy('order.source')
      .getRawMany();

    const ordersByPlatform: Record<PlatformSource, number> = {
      [PlatformSource.SHOPIFY]: 0,
      [PlatformSource.ETSY]: 0,
      [PlatformSource.AMAZON]: 0,
      [PlatformSource.ORDERS]: 0,
      [PlatformSource.MANUAL]: 0,
    };

    platformStats.forEach(stat => {
      if (stat.platform in ordersByPlatform) {
        ordersByPlatform[stat.platform as PlatformSource] = parseInt(stat.count);
      }
    });

    // Get orders by status
    const statusStats = await orderRepo
      .createQueryBuilder('order')
      .select('order.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('order.status')
      .getRawMany();

    const ordersByStatus: Record<OrderStatus, number> = {
      [OrderStatus.PENDING]: 0,
      [OrderStatus.CONFIRMED]: 0,
      [OrderStatus.PROCESSING]: 0,
      [OrderStatus.SHIPPED]: 0,
      [OrderStatus.DELIVERED]: 0,
      [OrderStatus.CANCELLED]: 0,
      [OrderStatus.REFUNDED]: 0,
      [OrderStatus.RETURNED]: 0,
    };

    statusStats.forEach(stat => {
      if (stat.status in ordersByStatus) {
        ordersByStatus[stat.status as OrderStatus] = parseInt(stat.count);
      }
    });

    // Get recent orders (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentOrders = await orderRepo.count({
      where: { externalCreatedAt: MoreThanOrEqual(sevenDaysAgo) },
    });

    // Calculate average order value
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return {
      totalOrders,
      totalRevenue,
      ordersByPlatform,
      ordersByStatus,
      recentOrders,
      averageOrderValue,
    };
  }

  /**
   * Get recent orders
   */
  async getRecentOrders(limit: number = 10): Promise<any[]> {
    this.logger.log(`Getting ${limit} recent orders`);

    const orderRepo = this.dataSource.getRepository(Order);
    const orders = await orderRepo.find({
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
      take: limit,
    });

    return orders.map(order => this.formatOrderForResponse(order));
  }

  /**
   * Format order for API response
   */
  private formatOrderForResponse(order: any): any {
    return {
      id: order.id,
      externalOrderId: order.externalOrderId,
      orderNumber: order.orderNumber,
      status: order.status,
      source: order.source,
      customerName: order.customerName,
      customerEmail: order.customerEmail,
      customerPhone: order.customerPhone,
      shippingAddress: {
        line1: order.shippingAddressLine1,
        line2: order.shippingAddressLine2,
        city: order.shippingCity,
        state: order.shippingState,
        postalCode: order.shippingPostalCode,
        country: order.shippingCountry,
      },
      subtotalPrice: order.subtotalPrice,
      shippingPrice: order.shippingPrice,
      taxAmount: order.taxAmount,
      discountAmount: order.discountAmount,
      totalPrice: order.totalPrice,
      currency: order.currency,
      customerNote: order.customerNote,
      isProcessed: order.isProcessed,
      externalCreatedAt: order.externalCreatedAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      orderItems:
        order.items?.map((item: any) => ({
          id: item.id,
          sku: item.sku,
          title: item.title,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          currency: item.currency,
          isCustom: item.isCustom,
          isEngrave: item.isEngrave,
          customText: item.customText,
          engraveText: item.engraveText,
        })) || [],
      metadata: order.metadata,
    };
  }
}
