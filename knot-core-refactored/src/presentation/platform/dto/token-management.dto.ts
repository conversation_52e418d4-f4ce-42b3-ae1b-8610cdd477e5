import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, IsObject, IsEnum } from 'class-validator';
import { TokenProvider } from '../../../domain/common/entities/oauth-token.entity';

export class StoreTokenDto {
  @ApiProperty({
    description: 'OAuth access token',
    example: 'ya29.a0AfH6SMC...',
  })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'OAuth refresh token',
    example: 'ya29.a0AfH6SMC...',
  })
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'Token expiration date',
    example: '2024-01-01T00:00:00Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiProperty({
    description: 'OAuth scopes granted',
    example: 'shops_r transactions_r',
    required: false,
  })
  @IsOptional()
  @IsString()
  scopes?: string;

  @ApiProperty({
    description: 'Associated shop/account ID',
    example: '6507168',
    required: false,
  })
  @IsOptional()
  @IsString()
  shopId?: string;

  @ApiProperty({
    description: 'Associated shop/account name',
    example: 'My Etsy Shop',
    required: false,
  })
  @IsOptional()
  @IsString()
  shopName?: string;

  @ApiProperty({
    description: 'Additional metadata',
    example: { source: 'manual_upload' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class TokenStatusResponseDto {
  @ApiProperty({
    description: 'Whether a token exists for this provider',
    example: true,
  })
  hasToken: boolean;

  @ApiProperty({
    description: 'Whether the token is valid and not expired',
    example: true,
  })
  isValid: boolean;

  @ApiProperty({
    description: 'Token expiration date',
    example: '2024-01-01T00:00:00Z',
    required: false,
  })
  expiresAt?: Date;

  @ApiProperty({
    description: 'Last time token was used',
    example: '2024-01-01T00:00:00Z',
    required: false,
  })
  lastUsedAt?: Date;

  @ApiProperty({
    description: 'Number of consecutive refresh failures',
    example: 0,
  })
  refreshFailureCount: number;
}

export class TokenStatisticsResponseDto {
  @ApiProperty({
    description: 'Total number of tokens',
    example: 5,
  })
  total: number;

  @ApiProperty({
    description: 'Number of active tokens',
    example: 3,
  })
  active: number;

  @ApiProperty({
    description: 'Number of expired tokens',
    example: 1,
  })
  expired: number;

  @ApiProperty({
    description: 'Number of revoked tokens',
    example: 1,
  })
  revoked: number;

  @ApiProperty({
    description: 'Number of tokens in error state',
    example: 0,
  })
  error: number;

  @ApiProperty({
    description: 'Token count by provider',
    example: { etsy: 2, shopify: 1, amazon: 0 },
  })
  byProvider: Record<string, number>;
}

export class RefreshTokenResponseDto {
  @ApiProperty({
    description: 'Whether the refresh was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Error message if refresh failed',
    example: 'Refresh token expired',
    required: false,
  })
  error?: string;

  @ApiProperty({
    description: 'New token expiration date',
    example: '2024-01-01T00:00:00Z',
    required: false,
  })
  expiresAt?: Date;
}
