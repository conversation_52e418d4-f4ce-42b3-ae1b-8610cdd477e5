import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
  Body,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { JwtAuthGuard } from '@presentation/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@presentation/auth/guards/roles.guard';
import { Roles } from '@presentation/auth/decorators/roles.decorator';
import { Public } from '@presentation/auth/decorators/public.decorator';
import { UserRole } from '@domain/user/user-role.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';

import { PlatformService, SyncResult, PlatformStatus } from './platform.service';
import { TokenManagerService } from '../../shared/services/token-manager.service';
import { TokenProvider } from '../../domain/common/entities/oauth-token.entity';
import {
  StoreTokenDto,
  TokenStatusResponseDto,
  TokenStatisticsResponseDto,
  RefreshTokenResponseDto,
} from './dto/token-management.dto';

@ApiTags('Platform Integration')
@Controller('platform')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class PlatformController {
  constructor(
    private readonly platformService: PlatformService,
    private readonly tokenManager: TokenManagerService,
  ) {}

  @Post('sync/all')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sync all platforms' })
  @ApiResponse({
    status: 200,
    description: 'Sync completed for all platforms',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              platform: { type: 'string', enum: Object.values(PlatformSource) },
              success: { type: 'boolean' },
              productsProcessed: { type: 'number' },
              ordersProcessed: { type: 'number' },
              errors: { type: 'array', items: { type: 'string' } },
              duration: { type: 'number' },
              timestamp: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
  })
  async syncAllPlatforms(): Promise<{ success: boolean; data: SyncResult[] }> {
    const results = await this.platformService.syncAllPlatforms();
    const overallSuccess = results.every(result => result.success);

    return {
      success: overallSuccess,
      data: results,
    };
  }

  @Post('sync/:platform')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sync specific platform' })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to sync',
  })
  @ApiResponse({
    status: 200,
    description: 'Platform sync completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            platform: { type: 'string', enum: Object.values(PlatformSource) },
            success: { type: 'boolean' },
            productsProcessed: { type: 'number' },
            ordersProcessed: { type: 'number' },
            errors: { type: 'array', items: { type: 'string' } },
            duration: { type: 'number' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  async syncPlatform(
    @Param('platform') platform: PlatformSource,
  ): Promise<{ success: boolean; data: SyncResult }> {
    const result = await this.platformService.syncPlatform(platform);

    return {
      success: result.success,
      data: result,
    };
  }

  @Post('sync/:platform/products')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sync products for specific platform' })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to sync products for',
  })
  @ApiResponse({
    status: 200,
    description: 'Product sync completed',
  })
  async syncPlatformProducts(
    @Param('platform') platform: PlatformSource,
  ): Promise<{ success: boolean; data: SyncResult }> {
    const result = await this.platformService.syncPlatformProducts(platform);

    return {
      success: result.success,
      data: result,
    };
  }

  @Post('sync/:platform/orders')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sync orders for specific platform' })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to sync orders for',
  })
  @ApiResponse({
    status: 200,
    description: 'Order sync completed',
  })
  async syncPlatformOrders(
    @Param('platform') platform: PlatformSource,
  ): Promise<{ success: boolean; data: SyncResult }> {
    const result = await this.platformService.syncPlatformOrders(platform);

    return {
      success: result.success,
      data: result,
    };
  }

  @Post('sync/last-week-orders')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sync orders from last week for Shopify and Etsy' })
  @ApiResponse({
    status: 200,
    description: 'Last week orders sync completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            shopify: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                ordersProcessed: { type: 'number' },
                errors: { type: 'array', items: { type: 'string' } },
              },
            },
            etsy: {
              type: 'object',
              properties: {
                success: { type: 'boolean' },
                ordersProcessed: { type: 'number' },
                errors: { type: 'array', items: { type: 'string' } },
              },
            },
          },
        },
      },
    },
  })
  async syncLastWeekOrders(): Promise<{
    success: boolean;
    data: {
      shopify: SyncResult;
      etsy: SyncResult;
    };
  }> {
    // Sync orders from last week for both platforms
    const [shopifyResult, etsyResult] = await Promise.allSettled([
      this.platformService.syncPlatformOrders(PlatformSource.SHOPIFY),
      this.platformService.syncPlatformOrders(PlatformSource.ETSY),
    ]);

    const shopify =
      shopifyResult.status === 'fulfilled'
        ? shopifyResult.value
        : ({
            platform: PlatformSource.SHOPIFY,
            success: false,
            productsProcessed: 0,
            ordersProcessed: 0,
            errors: [shopifyResult.reason?.message || 'Unknown error'],
            duration: 0,
            timestamp: new Date(),
          } as SyncResult);

    const etsy =
      etsyResult.status === 'fulfilled'
        ? etsyResult.value
        : ({
            platform: PlatformSource.ETSY,
            success: false,
            productsProcessed: 0,
            ordersProcessed: 0,
            errors: [etsyResult.reason?.message || 'Unknown error'],
            duration: 0,
            timestamp: new Date(),
          } as SyncResult);

    const overallSuccess = shopify.success && etsy.success;

    return {
      success: overallSuccess,
      data: {
        shopify,
        etsy,
      },
    };
  }

  @Get('status')
  @Public()
  @ApiOperation({ summary: 'Get status of all platforms' })
  @ApiResponse({
    status: 200,
    description: 'Platform statuses retrieved',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              platform: { type: 'string', enum: Object.values(PlatformSource) },
              isHealthy: { type: 'boolean' },
              lastSync: { type: 'string', format: 'date-time', nullable: true },
              lastError: { type: 'string', nullable: true },
              apiStatus: { type: 'string', enum: ['connected', 'disconnected', 'error'] },
            },
          },
        },
      },
    },
  })
  async getAllPlatformStatuses(): Promise<{ success: boolean; data: PlatformStatus[] }> {
    const statuses = await this.platformService.getAllPlatformStatuses();

    return {
      success: true,
      data: statuses,
    };
  }

  @Get('status/:platform')
  @Public()
  @ApiOperation({ summary: 'Get status of specific platform' })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to get status for',
  })
  @ApiResponse({
    status: 200,
    description: 'Platform status retrieved',
  })
  async getPlatformStatus(
    @Param('platform') platform: PlatformSource,
  ): Promise<{ success: boolean; data: PlatformStatus }> {
    const status = await this.platformService.getPlatformStatus(platform);

    return {
      success: true,
      data: status,
    };
  }

  @Post('inventory/update')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update inventory across all platforms' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        sku: { type: 'string', description: 'Product SKU' },
        quantity: { type: 'number', description: 'New quantity' },
      },
      required: ['sku', 'quantity'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Inventory update completed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            results: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  platform: { type: 'string', enum: Object.values(PlatformSource) },
                  success: { type: 'boolean' },
                  error: { type: 'string', nullable: true },
                },
              },
            },
          },
        },
      },
    },
  })
  async updateInventoryAcrossPlatforms(@Body() body: { sku: string; quantity: number }): Promise<{
    success: boolean;
    data: {
      success: boolean;
      results: Array<{
        platform: PlatformSource;
        success: boolean;
        error?: string;
      }>;
    };
  }> {
    const result = await this.platformService.updateInventoryAcrossPlatforms(
      body.sku,
      body.quantity,
    );

    return {
      success: result.success,
      data: result,
    };
  }

  @Get('statistics')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get sync statistics' })
  @ApiResponse({
    status: 200,
    description: 'Sync statistics retrieved',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalProducts: { type: 'number' },
            totalOrders: { type: 'number' },
            lastSyncTimes: {
              type: 'object',
              additionalProperties: {
                type: 'string',
                format: 'date-time',
                nullable: true,
              },
            },
            syncCounts: {
              type: 'object',
              additionalProperties: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getSyncStatistics(): Promise<{
    success: boolean;
    data: {
      totalProducts: number;
      totalOrders: number;
      lastSyncTimes: Record<PlatformSource, Date | null>;
      syncCounts: Record<PlatformSource, number>;
    };
  }> {
    const statistics = await this.platformService.getSyncStatistics();

    return {
      success: true,
      data: statistics,
    };
  }

  @Get('history')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get sync history' })
  @ApiQuery({
    name: 'platform',
    enum: PlatformSource,
    required: false,
    description: 'Filter by platform',
  })
  @ApiQuery({
    name: 'limit',
    type: 'number',
    required: false,
    description: 'Number of entries to return (default: 50)',
  })
  @ApiResponse({
    status: 200,
    description: 'Sync history retrieved',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              platform: { type: 'string', enum: Object.values(PlatformSource) },
              timestamp: { type: 'string', format: 'date-time' },
              success: { type: 'boolean' },
              productsProcessed: { type: 'number' },
              ordersProcessed: { type: 'number' },
              duration: { type: 'number' },
              errors: { type: 'array', items: { type: 'string' } },
            },
          },
        },
      },
    },
  })
  async getSyncHistory(
    @Query('platform') platform?: PlatformSource,
    @Query('limit') limit?: number,
  ): Promise<{
    success: boolean;
    data: Array<{
      platform: PlatformSource;
      timestamp: Date;
      success: boolean;
      productsProcessed: number;
      ordersProcessed: number;
      duration: number;
      errors: string[];
    }>;
  }> {
    const history = await this.platformService.getSyncHistory(platform, limit);

    return {
      success: true,
      data: history,
    };
  }

  /**
   * Sync orders from specific platform with date filters
   */
  @Post('sync/orders/:platform')
  @Public()
  @ApiOperation({
    summary: 'Sync orders from platform with date filters',
    description: `
      Sync orders from a specific platform (Shopify, Etsy, or Amazon) with optional date range filters.

      **Features:**
      - ✅ **Date Range Filtering** - Sync orders within specific date range
      - ✅ **Duplicate Prevention** - Updates existing orders instead of creating duplicates
      - ✅ **Batch Processing** - Configurable limit for performance
      - ✅ **Force Update** - Option to force update existing orders

      **Usage Examples:**
      - Sync last 30 days: \`startDate: "2024-01-01T00:00:00Z"\`
      - Sync specific range: \`startDate: "2024-01-01", endDate: "2024-01-31"\`
      - Limit results: \`limit: 100\`
    `,
  })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to sync from',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for sync (ISO 8601)',
    example: '2024-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for sync (ISO 8601)',
    example: '2024-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Max orders to sync (1-250)',
    example: 50,
  })
  @ApiQuery({
    name: 'forceUpdate',
    required: false,
    description: 'Force update existing orders',
    example: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Orders synced successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            platform: { type: 'string' },
            ordersProcessed: { type: 'number' },
            ordersCreated: { type: 'number' },
            ordersUpdated: { type: 'number' },
            ordersSkipped: { type: 'number' },
            duration: { type: 'number' },
            dateRange: {
              type: 'object',
              properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' },
              },
            },
            errors: { type: 'array', items: { type: 'string' } },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid parameters or platform not supported',
  })
  @ApiResponse({
    status: 500,
    description: 'Sync failed due to platform API error',
  })
  async syncOrders(
    @Param('platform') platform: PlatformSource,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
    @Query('forceUpdate') forceUpdate?: boolean,
  ): Promise<{
    success: boolean;
    data: {
      platform: PlatformSource;
      ordersProcessed: number;
      ordersCreated: number;
      ordersUpdated: number;
      ordersSkipped: number;
      duration: number;
      dateRange: {
        startDate?: string;
        endDate?: string;
      };
      errors: string[];
    };
  }> {
    const result = await this.platformService.syncOrdersWithFilters({
      platform,
      startDate,
      endDate,
      limit: limit || 50,
      forceUpdate: forceUpdate || false,
    });

    return {
      success: true,
      data: result,
    };
  }

  /**
   * Start queue-based order sync for large datasets
   */
  @Post('sync/orders/:platform/queue')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Start queue-based order sync for large datasets',
    description: `
      Start a queue-based order sync for processing large datasets efficiently.
      This endpoint is recommended for syncing more than 100 orders or date ranges longer than 30 days.

      **Features:**
      - ✅ **Background Processing** - Non-blocking queue-based processing
      - ✅ **Progress Tracking** - Monitor sync progress via status endpoint
      - ✅ **Rate Limiting** - Respects platform API rate limits
      - ✅ **Error Handling** - Robust error handling with retries
      - ✅ **Batch Processing** - Processes orders in configurable batches
    `,
  })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to sync from',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for sync (ISO 8601)',
    example: '2024-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for sync (ISO 8601)',
    example: '2024-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Max orders to sync (default: 1000)',
    example: 1000,
  })
  @ApiQuery({
    name: 'forceUpdate',
    required: false,
    description: 'Force update existing orders',
    example: false,
  })
  @ApiQuery({
    name: 'batchSize',
    required: false,
    description: 'Orders per batch (default: 50)',
    example: 50,
  })
  @ApiResponse({
    status: 200,
    description: 'Queue-based sync started successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            jobId: { type: 'string' },
            message: { type: 'string' },
            estimatedBatches: { type: 'number' },
          },
        },
      },
    },
  })
  async startQueueOrderSync(
    @Param('platform') platform: PlatformSource,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
    @Query('forceUpdate') forceUpdate?: boolean,
    @Query('batchSize') batchSize?: number,
  ): Promise<{
    success: boolean;
    data: {
      jobId: string;
      message: string;
      estimatedBatches: number;
    };
  }> {
    const jobId = await this.platformService.startQueueBasedOrderSync(platform, {
      startDate,
      endDate,
      limit: limit || 1000,
      forceUpdate: forceUpdate || false,
      batchSize: batchSize || 50,
    });

    const estimatedBatches = Math.ceil((limit || 1000) / (batchSize || 50));

    return {
      success: true,
      data: {
        jobId,
        message:
          'Queue-based order sync started successfully. Use the status endpoint to monitor progress.',
        estimatedBatches,
      },
    };
  }

  /**
   * Get queue sync status
   */
  @Get('sync/orders/:platform/queue/:jobId/status')
  @Public()
  @ApiOperation({
    summary: 'Get queue-based sync status',
    description: 'Monitor the progress and status of a queue-based order sync job.',
  })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform being synced',
  })
  @ApiParam({
    name: 'jobId',
    description: 'Job ID returned from queue sync start',
  })
  @ApiResponse({
    status: 200,
    description: 'Sync status retrieved successfully',
  })
  async getQueueSyncStatus(
    @Param('platform') platform: PlatformSource,
    @Param('jobId') jobId: string,
  ) {
    const status = await this.platformService.getQueueSyncStatus(platform, jobId);

    if (!status) {
      throw new NotFoundException(`Sync job ${jobId} not found`);
    }

    return {
      success: true,
      data: status,
    };
  }

  /**
   * Cancel queue sync
   */
  @Delete('sync/orders/:platform/queue/:jobId')
  @Public()
  @ApiOperation({
    summary: 'Cancel queue-based sync',
    description: 'Cancel a running queue-based order sync job.',
  })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform being synced',
  })
  @ApiParam({
    name: 'jobId',
    description: 'Job ID to cancel',
  })
  async cancelQueueSync(
    @Param('platform') platform: PlatformSource,
    @Param('jobId') jobId: string,
  ) {
    const cancelled = await this.platformService.cancelQueueSync(platform, jobId);

    return {
      success: cancelled,
      message: cancelled ? 'Sync job cancelled successfully' : 'Failed to cancel sync job',
    };
  }

  /**
   * Get all active queue syncs
   */
  @Get('sync/orders/:platform/queue/active')
  @Public()
  @ApiOperation({
    summary: 'Get active queue syncs',
    description: 'Get all currently active queue-based sync jobs for a platform.',
  })
  @ApiParam({
    name: 'platform',
    enum: PlatformSource,
    description: 'Platform to check',
  })
  async getActiveQueueSyncs(@Param('platform') platform: PlatformSource) {
    const activeSyncs = await this.platformService.getActiveQueueSyncs(platform);

    return {
      success: true,
      data: activeSyncs,
    };
  }

  /**
   * Sync orders from all platforms
   */
  @Post('sync/orders/all')
  @Public()
  @ApiOperation({
    summary: 'Sync orders from all platforms',
    description:
      'Sync orders from all configured platforms (Shopify, Etsy, Amazon) with the same filters',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date for sync (ISO 8601)',
    example: '2024-01-01T00:00:00Z',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date for sync (ISO 8601)',
    example: '2024-12-31T23:59:59Z',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Max orders per platform (1-250)',
    example: 50,
  })
  @ApiQuery({
    name: 'forceUpdate',
    required: false,
    description: 'Force update existing orders',
    example: false,
  })
  @ApiResponse({
    status: 200,
    description: 'Orders synced from all platforms',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalOrdersProcessed: { type: 'number' },
            totalOrdersCreated: { type: 'number' },
            totalOrdersUpdated: { type: 'number' },
            totalDuration: { type: 'number' },
            platforms: {
              type: 'object',
              additionalProperties: {
                type: 'object',
                properties: {
                  ordersProcessed: { type: 'number' },
                  ordersCreated: { type: 'number' },
                  ordersUpdated: { type: 'number' },
                  ordersSkipped: { type: 'number' },
                  duration: { type: 'number' },
                  errors: { type: 'array', items: { type: 'string' } },
                },
              },
            },
          },
        },
      },
    },
  })
  async syncAllOrders(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: number,
    @Query('forceUpdate') forceUpdate?: boolean,
  ): Promise<{
    success: boolean;
    data: {
      totalOrdersProcessed: number;
      totalOrdersCreated: number;
      totalOrdersUpdated: number;
      totalDuration: number;
      platforms: Record<string, any>;
    };
  }> {
    const result = await this.platformService.syncAllOrdersWithFilters({
      startDate,
      endDate,
      limit: limit || 50,
      forceUpdate: forceUpdate || false,
    });

    return {
      success: true,
      data: result,
    };
  }

  // Token Management Endpoints

  @Post('tokens/:platform')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Store OAuth tokens for a platform' })
  @ApiParam({
    name: 'platform',
    enum: ['etsy', 'shopify', 'amazon'],
    description: 'Platform to store tokens for',
  })
  @ApiBody({ type: StoreTokenDto })
  @ApiResponse({
    status: 200,
    description: 'Tokens stored successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async storeTokens(
    @Param('platform') platform: string,
    @Body() storeTokenDto: StoreTokenDto,
  ): Promise<{ success: boolean; message: string }> {
    const tokenProvider = platform.toUpperCase() as TokenProvider;

    await this.tokenManager.storeTokens(tokenProvider, {
      accessToken: storeTokenDto.accessToken,
      refreshToken: storeTokenDto.refreshToken,
      expiresAt: storeTokenDto.expiresAt ? new Date(storeTokenDto.expiresAt) : undefined,
      scopes: storeTokenDto.scopes,
      shopId: storeTokenDto.shopId,
      shopName: storeTokenDto.shopName,
      metadata: storeTokenDto.metadata,
    });

    return {
      success: true,
      message: `Tokens stored successfully for ${platform}`,
    };
  }

  @Get('tokens/:platform/status')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get token status for a platform' })
  @ApiParam({
    name: 'platform',
    enum: ['etsy', 'shopify', 'amazon'],
    description: 'Platform to check token status for',
  })
  @ApiQuery({
    name: 'shopId',
    required: false,
    description: 'Shop ID to check tokens for',
  })
  @ApiResponse({
    status: 200,
    description: 'Token status retrieved successfully',
    type: TokenStatusResponseDto,
  })
  async getTokenStatus(
    @Param('platform') platform: string,
    @Query('shopId') shopId?: string,
  ): Promise<TokenStatusResponseDto> {
    const tokenProvider = platform.toUpperCase() as TokenProvider;
    return this.tokenManager.getTokenStatus(tokenProvider, shopId);
  }

  @Post('tokens/:platform/refresh')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Manually refresh tokens for a platform' })
  @ApiParam({
    name: 'platform',
    enum: ['etsy', 'shopify', 'amazon'],
    description: 'Platform to refresh tokens for',
  })
  @ApiQuery({
    name: 'shopId',
    required: false,
    description: 'Shop ID to refresh tokens for',
  })
  @ApiResponse({
    status: 200,
    description: 'Token refresh result',
    type: RefreshTokenResponseDto,
  })
  async refreshTokens(
    @Param('platform') platform: string,
    @Query('shopId') shopId?: string,
  ): Promise<RefreshTokenResponseDto> {
    try {
      const tokenProvider = platform.toUpperCase() as TokenProvider;
      const accessToken = await this.tokenManager.getValidAccessToken(tokenProvider, shopId);

      if (accessToken) {
        const status = await this.tokenManager.getTokenStatus(tokenProvider, shopId);
        return {
          success: true,
          expiresAt: status.expiresAt,
        };
      } else {
        return {
          success: false,
          error: 'Failed to refresh token',
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  @Delete('tokens/:platform')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Revoke tokens for a platform' })
  @ApiParam({
    name: 'platform',
    enum: ['etsy', 'shopify', 'amazon'],
    description: 'Platform to revoke tokens for',
  })
  @ApiQuery({
    name: 'shopId',
    required: false,
    description: 'Shop ID to revoke tokens for',
  })
  @ApiResponse({
    status: 200,
    description: 'Tokens revoked successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
      },
    },
  })
  async revokeTokens(
    @Param('platform') platform: string,
    @Query('shopId') shopId?: string,
  ): Promise<{ success: boolean; message: string }> {
    const tokenProvider = platform.toUpperCase() as TokenProvider;
    await this.tokenManager.revokeTokens(tokenProvider, shopId);

    return {
      success: true,
      message: `Tokens revoked successfully for ${platform}${shopId ? ` (shop: ${shopId})` : ''}`,
    };
  }

  @Get('tokens/statistics')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get token statistics' })
  @ApiResponse({
    status: 200,
    description: 'Token statistics retrieved successfully',
    type: TokenStatisticsResponseDto,
  })
  async getTokenStatistics(): Promise<TokenStatisticsResponseDto> {
    return this.tokenManager.getTokenStatistics();
  }

  @Post('tokens/etsy/store-current')
  @Roles(UserRole.ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Store the current Etsy tokens securely' })
  @ApiResponse({
    status: 200,
    description: 'Current Etsy tokens stored successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        tokenStatus: {
          type: 'object',
          properties: {
            hasToken: { type: 'boolean' },
            isValid: { type: 'boolean' },
            expiresAt: { type: 'string' },
          },
        },
      },
    },
  })
  async storeCurrentEtsyTokens(): Promise<{
    success: boolean;
    message: string;
    tokenStatus: any;
  }> {
    try {
      // Store the current fresh tokens
      await this.tokenManager.storeTokens(TokenProvider.ETSY, {
        accessToken:
          '13208796.z1TsHlJOhv88arTV_rQS_oG737mLcgrDT2Vz9MNyWWQn_f4rHzbLyMoTR0jnEeQAWCRPt9BXN3Dovfo2h4IOsCFWw1I',
        refreshToken:
          '13208796.6WTgIPwpBpwd1kvVnQLlVSsMnjhX6E6C9US2kLzt53TUZKX8SSNjWSDI2ONVEDfIIWN7uENnBEoKJbId-Thsc-k09se',
        shopId: '6507168',
        scopes: 'shops_r transactions_r',
        expiresAt: new Date(Date.now() + 3600 * 1000), // 1 hour from now
        metadata: {
          storedAt: new Date().toISOString(),
          source: 'fresh_tokens_2025_08_05',
        },
      });

      // Get the updated token status
      const tokenStatus = await this.tokenManager.getTokenStatus(TokenProvider.ETSY, '6507168');

      return {
        success: true,
        message: 'Current Etsy tokens stored successfully and will be automatically refreshed',
        tokenStatus,
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to store current Etsy tokens: ${error.message}`,
        tokenStatus: null,
      };
    }
  }
}
