import { Process, Processor } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EtsyApiService } from '@infrastructure/external/etsy/etsy-api.service';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';

export interface EtsyOrderSyncJobData {
  startDate?: string;
  endDate?: string;
  offset: number;
  limit: number;
  totalExpected?: number;
  jobId: string;
  forceUpdate: boolean;
}

export interface EtsyOrderSyncJobResult {
  ordersProcessed: number;
  ordersCreated: number;
  ordersUpdated: number;
  ordersSkipped: number;
  hasMore: boolean;
  nextOffset?: number;
  errors: string[];
}

@Processor('etsy-order-sync')
@Injectable()
export class EtsyOrderSyncProcessor {
  private readonly logger = new Logger(EtsyOrderSyncProcessor.name);

  constructor(
    private readonly etsyApiService: EtsyApiService,
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
  ) {}

  @Process('sync-orders-batch')
  async handleOrderSyncBatch(job: Job<EtsyOrderSyncJobData>): Promise<EtsyOrderSyncJobResult> {
    const { startDate, endDate, offset, limit, jobId, forceUpdate } = job.data;

    this.logger.log(
      `Processing Etsy order sync batch - Job: ${jobId}, Offset: ${offset}, Limit: ${limit}`,
    );

    let ordersProcessed = 0;
    let ordersCreated = 0;
    let ordersUpdated = 0;
    let ordersSkipped = 0;
    const errors: string[] = [];

    try {
      // Update job progress
      await job.progress(10);

      // Build Etsy API parameters
      const apiParams: any = {
        limit: Math.min(limit, 100), // Etsy max limit
        offset,
        sort_on: 'created',
        sort_order: 'down',
      };

      // Add date filters if provided (Etsy uses Unix timestamps)
      if (startDate) {
        const startTimestamp = Math.floor(new Date(startDate).getTime() / 1000);
        apiParams.min_created = startTimestamp;
      }
      if (endDate) {
        const endTimestamp = Math.floor(new Date(endDate).getTime() / 1000);
        apiParams.max_created = endTimestamp;
      }

      this.logger.log(`Fetching receipts from Etsy API with params:`, apiParams);

      // Update job progress
      await job.progress(30);

      // Fetch receipts from Etsy with rate limiting
      const receiptsResponse = await this.etsyApiService.getShopReceipts(apiParams);
      const receipts = receiptsResponse.receipts;

      this.logger.log(`Retrieved ${receipts.length} receipts from Etsy`);

      // Update job progress
      await job.progress(50);

      // Process each receipt
      for (let i = 0; i < receipts.length; i++) {
        const receipt = receipts[i];

        try {
          ordersProcessed++;

          // Check if order already exists
          const existingOrder = await this.orderRepository.findByExternalId(
            receipt.receipt_id?.toString() || '',
            PlatformSource.ETSY,
          );

          if (existingOrder) {
            if (forceUpdate) {
              // Update existing order
              await this.updateExistingOrder(existingOrder, receipt);
              await this.orderRepository.save(existingOrder);
              ordersUpdated++;
              this.logger.debug(`Updated existing Etsy order: ${receipt.receipt_id}`);
            } else {
              // Skip existing order
              ordersSkipped++;
              this.logger.debug(`Skipped existing Etsy order: ${receipt.receipt_id}`);
            }
          } else {
            // Create new order
            await this.processReceipt(receipt);
            ordersCreated++;
            this.logger.debug(`Created new Etsy order: ${receipt.receipt_id}`);
          }

          // Update progress based on processed items
          const progressPercent = 50 + Math.floor((i / receipts.length) * 40);
          await job.progress(progressPercent);

          // Add small delay to respect rate limits
          if (i < receipts.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 100));
          }
        } catch (error) {
          const errorMsg = `Failed to process Etsy receipt ${receipt.receipt_id}: ${error.message}`;
          this.logger.error(errorMsg, error);
          errors.push(errorMsg);
          // Continue processing other receipts
        }
      }

      // Determine if there are more records
      const hasMore = receipts.length === limit;
      const nextOffset = hasMore ? offset + limit : undefined;

      // Update job progress to complete
      await job.progress(100);

      const result: EtsyOrderSyncJobResult = {
        ordersProcessed,
        ordersCreated,
        ordersUpdated,
        ordersSkipped,
        hasMore,
        nextOffset,
        errors,
      };

      this.logger.log(
        `Etsy order sync batch completed - Job: ${jobId}, Processed: ${ordersProcessed}, Created: ${ordersCreated}, Updated: ${ordersUpdated}, Skipped: ${ordersSkipped}, HasMore: ${hasMore}`,
      );

      return result;
    } catch (error) {
      this.logger.error(`Etsy order sync batch failed - Job: ${jobId}:`, error);
      errors.push(`Batch processing failed: ${error.message}`);

      throw error; // This will mark the job as failed
    }
  }

  /**
   * Process receipt from Etsy and create order
   */
  private async processReceipt(receipt: any): Promise<void> {
    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        this.logger.warn(`Invalid timestamp received: ${timestamp}, using current date`);
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    // Create new order
    const order = this.orderRepository.create({
      externalOrderId: receipt.receipt_id?.toString() || '',
      orderNumber: receipt.receipt_id?.toString() || '',
      status: this.mapEtsyOrderStatus(receipt.was_paid, receipt.was_shipped),
      source: PlatformSource.ETSY,
      externalCreatedAt: safeTimestampToDate(receipt.creation_timestamp),
      customerEmail: receipt.buyer_email || '',
      customerName: receipt.name || '',
      customerPhone: '',
      shippingAddressLine1: receipt.first_line || '',
      shippingAddressLine2: receipt.second_line || '',
      shippingCity: receipt.city || '',
      shippingState: receipt.state || '',
      shippingPostalCode: receipt.zip || '',
      shippingCountry: receipt.country_iso || '',
      subtotalPrice: safePriceValue(receipt.subtotal),
      shippingPrice: safePriceValue(receipt.total_shipping_cost),
      taxAmount: safePriceValue(receipt.total_tax_cost),
      discountAmount: safePriceValue(receipt.discount_amt),
      totalPrice: safePriceValue(receipt.grandtotal),
      currency: receipt.currency_code || 'USD',
      customerNote: receipt.message_from_buyer || '',
      isProcessed: false,
      rawData: {
        platform: 'etsy',
        receipt: receipt,
        syncedAt: new Date().toISOString(),
        processorVersion: '1.0',
      },
      metadata: {
        etsyData: {
          orderId: receipt.order_id,
          paymentMethod: receipt.payment_method,
          wasPaid: receipt.was_paid,
          needsGiftWrap: receipt.needs_gift_wrap,
          giftMessage: receipt.gift_message,
        },
      },
    });

    const savedOrder = await this.orderRepository.save(order);

    // Create order items from transactions
    for (const transaction of receipt.transactions) {
      await this.createOrderItem(savedOrder.id, transaction);
    }
  }

  /**
   * Update existing order with Etsy data
   */
  private async updateExistingOrder(existingOrder: any, receipt: any): Promise<void> {
    const newStatus = this.mapEtsyOrderStatus(receipt.was_paid, receipt.was_shipped);

    if (existingOrder.status !== newStatus) {
      existingOrder.updateStatus(newStatus);
    }

    // Helper function to safely convert timestamp
    const safeTimestampToDate = (timestamp: any): Date => {
      if (!timestamp || isNaN(timestamp)) {
        return new Date();
      }
      return new Date(Number(timestamp) * 1000);
    };

    // Update metadata
    if (!existingOrder.metadata) {
      existingOrder.metadata = {};
    }
    existingOrder.metadata.etsyData = {
      ...existingOrder.metadata.etsyData,
      lastUpdated: safeTimestampToDate(receipt.update_timestamp),
      wasPaid: receipt.was_paid,
      wasShipped: receipt.was_shipped,
    };
  }

  /**
   * Create order item from Etsy transaction
   */
  private async createOrderItem(orderId: string, transaction: any): Promise<void> {
    const sku =
      transaction.sku || `ETSY-${transaction.listing_id}-${transaction.product_id || 'unknown'}`;

    // Helper function to safely get price value
    const safePriceValue = (priceObj: any): number => {
      if (!priceObj || !priceObj.amount || !priceObj.divisor) {
        return 0;
      }
      return Number(priceObj.amount) / Number(priceObj.divisor);
    };

    const unitPrice = safePriceValue(transaction.price);
    const quantity = Number(transaction.quantity) || 1;

    const orderItem = this.orderItemRepository.create({
      orderId,
      sku,
      title: transaction.title || '',
      quantity,
      unitPrice,
      totalPrice: unitPrice * quantity,
      currency: transaction.price?.currency_code || 'USD',
      rawData: {
        platform: 'etsy',
        transaction: transaction,
        syncedAt: new Date().toISOString(),
      },
      metadata: {
        etsyData: {
          transactionId: transaction.transaction_id,
          listingId: transaction.listing_id,
          productId: transaction.product_id,
          variationData: transaction.variations,
        },
      },
    });

    await this.orderItemRepository.save(orderItem);
  }

  /**
   * Map Etsy order status to internal status
   */
  private mapEtsyOrderStatus(wasPaid: boolean, wasShipped: boolean): OrderStatus {
    if (wasShipped) {
      return OrderStatus.SHIPPED;
    }
    if (wasPaid) {
      return OrderStatus.CONFIRMED;
    }
    return OrderStatus.PENDING;
  }
}
