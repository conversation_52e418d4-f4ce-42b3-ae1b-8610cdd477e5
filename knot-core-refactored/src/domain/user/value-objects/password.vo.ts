import { ValueObject } from '@domain/common/value-object';
import * as bcrypt from 'bcrypt';

interface PasswordProps {
  value: string;
  isHashed: boolean;
}

export class Password extends ValueObject<PasswordProps> {
  private static readonly SALT_ROUNDS = 12;
  private static readonly MIN_LENGTH = 8;
  private static readonly MAX_LENGTH = 128;

  constructor(password: string, isHashed: boolean = false) {
    if (!isHashed && !Password.isValid(password)) {
      throw new Error('Password does not meet security requirements');
    }

    super({ value: password, isHashed });
  }

  /**
   * Get password value (hashed or plain)
   */
  get passwordValue(): string {
    return this.props.value;
  }

  /**
   * Check if password is hashed
   */
  get isHashed(): boolean {
    return this.props.isHashed;
  }

  /**
   * Hash the password
   */
  async hash(): Promise<Password> {
    if (this.props.isHashed) {
      return this;
    }

    const hashedValue = await bcrypt.hash(this.props.value, Password.SALT_ROUNDS);
    return new Password(hashedValue, true);
  }

  /**
   * Compare with another password
   */
  async compare(plainPassword: string): Promise<boolean> {
    if (!this.props.isHashed) {
      return this.props.value === plainPassword;
    }

    return bcrypt.compare(plainPassword, this.props.value);
  }

  /**
   * Validate password strength
   */
  static isValid(password: string): boolean {
    if (!password || typeof password !== 'string') {
      return false;
    }

    // Check length
    if (password.length < Password.MIN_LENGTH || password.length > Password.MAX_LENGTH) {
      return false;
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      return false;
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      return false;
    }

    // Check for at least one digit
    if (!/\d/.test(password)) {
      return false;
    }

    // Check for at least one special character
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      return false;
    }

    return true;
  }

  /**
   * Get password validation errors
   */
  static getValidationErrors(password: string): string[] {
    const errors: string[] = [];

    if (!password || typeof password !== 'string') {
      errors.push('Password is required');
      return errors;
    }

    if (password.length < Password.MIN_LENGTH) {
      errors.push(`Password must be at least ${Password.MIN_LENGTH} characters long`);
    }

    if (password.length > Password.MAX_LENGTH) {
      errors.push(`Password must not exceed ${Password.MAX_LENGTH} characters`);
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one digit');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return errors;
  }

  /**
   * Calculate password strength score (0-100)
   */
  static calculateStrength(password: string): number {
    if (!password) return 0;

    let score = 0;

    // Length bonus
    score += Math.min(password.length * 4, 25);

    // Character variety bonus
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/\d/.test(password)) score += 5;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 10;

    // Additional complexity bonus
    if (password.length >= 12) score += 10;
    if (/[a-z].*[A-Z]|[A-Z].*[a-z]/.test(password)) score += 5;
    if (/\d.*[!@#$%^&*(),.?":{}|<>]|[!@#$%^&*(),.?":{}|<>].*\d/.test(password)) score += 5;

    // Penalty for common patterns
    if (/(.)\1{2,}/.test(password)) score -= 10; // Repeated characters
    if (/123|abc|qwe/i.test(password)) score -= 10; // Sequential patterns

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Get password strength level
   */
  static getStrengthLevel(password: string): 'weak' | 'fair' | 'good' | 'strong' {
    const score = Password.calculateStrength(password);

    if (score < 30) return 'weak';
    if (score < 60) return 'fair';
    if (score < 80) return 'good';
    return 'strong';
  }

  /**
   * Generate a secure random password
   */
  static generate(length: number = 16): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const digits = '0123456789';
    const special = '!@#$%^&*(),.?":{}|<>';

    const allChars = lowercase + uppercase + digits + special;

    let password = '';

    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += digits[Math.floor(Math.random() * digits.length)];
    password += special[Math.floor(Math.random() * special.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }

  /**
   * Create password from string
   */
  static create(password: string): Password {
    return new Password(password);
  }

  /**
   * Create hashed password from string
   */
  static createHashed(hashedPassword: string): Password {
    return new Password(hashedPassword, true);
  }
}
