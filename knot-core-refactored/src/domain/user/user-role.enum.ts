export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

export const USER_ROLE_DESCRIPTIONS = {
  [UserRole.ADMIN]: 'Administrator with full system access',
  [UserRole.USER]: 'Regular user with limited access',
} as const;

export const USER_ROLE_PERMISSIONS = {
  [UserRole.ADMIN]: [
    'user:read',
    'user:write',
    'user:delete',
    'product:read',
    'product:write',
    'product:delete',
    'order:read',
    'order:write',
    'order:delete',
    'platform:read',
    'platform:write',
    'platform:delete',
    'system:read',
    'system:write',
  ],
  [UserRole.USER]: [
    'product:read',
    'order:read',
    'platform:read',
  ],
} as const;

/**
 * Check if role has permission
 */
export function hasPermission(role: UserRole, permission: string): boolean {
  return USER_ROLE_PERMISSIONS[role].includes(permission as any);
}

/**
 * Get all permissions for role
 */
export function getPermissions(role: UserRole): readonly string[] {
  return USER_ROLE_PERMISSIONS[role];
}

/**
 * Get role description
 */
export function getRoleDescription(role: UserRole): string {
  return USER_ROLE_DESCRIPTIONS[role];
}

/**
 * Check if role is admin
 */
export function isAdminRole(role: UserRole): boolean {
  return role === UserRole.ADMIN;
}

/**
 * Check if role is user
 */
export function isUserRole(role: UserRole): boolean {
  return role === UserRole.USER;
}
