import { ValueObject } from '@domain/common/value-object';

interface SKUProps {
  value: string;
}

export interface SKUParts {
  style?: string;
  color?: string;
  size?: number;
  design?: string;
}

export class SKU extends ValueObject<SKUProps> {
  private static readonly MIN_LENGTH = 3;
  private static readonly MAX_LENGTH = 50;
  private static readonly VALID_PATTERN = /^[A-Za-z0-9_-]+$/;

  constructor(sku: string) {
    if (!SKU.isValid(sku)) {
      throw new Error(`Invalid SKU format: ${sku}`);
    }

    super({ value: sku.toUpperCase().trim() });
  }

  /**
   * Get SKU value
   */
  get skuValue(): string {
    return this.props.value;
  }

  /**
   * Extract parts from SKU (basic implementation)
   * This would need to be customized based on your SKU format
   */
  extractParts(): SKUParts {
    const parts = this.props.value.split('-');
    const result: SKUParts = {};

    // Basic parsing - customize based on your SKU format
    if (parts.length >= 1) {
      result.style = parts[0];
    }
    if (parts.length >= 2) {
      result.color = parts[1];
    }
    if (parts.length >= 3) {
      const sizeMatch = parts[2].match(/\d+/);
      if (sizeMatch) {
        result.size = parseInt(sizeMatch[0], 10);
      }
    }
    if (parts.length >= 4) {
      result.design = parts[3];
    }

    return result;
  }

  /**
   * Check if SKU contains specific part
   */
  contains(part: string): boolean {
    return this.props.value.includes(part.toUpperCase());
  }

  /**
   * Get SKU prefix (first part before delimiter)
   */
  getPrefix(delimiter: string = '-'): string {
    const parts = this.props.value.split(delimiter);
    return parts[0] || '';
  }

  /**
   * Get SKU suffix (last part after delimiter)
   */
  getSuffix(delimiter: string = '-'): string {
    const parts = this.props.value.split(delimiter);
    return parts[parts.length - 1] || '';
  }

  /**
   * Check if SKU matches pattern
   */
  matchesPattern(pattern: RegExp): boolean {
    return pattern.test(this.props.value);
  }

  /**
   * Generate variant SKU
   */
  generateVariant(suffix: string): SKU {
    return new SKU(`${this.props.value}-${suffix.toUpperCase()}`);
  }

  /**
   * Validate SKU format
   */
  static isValid(sku: string): boolean {
    if (!sku || typeof sku !== 'string') {
      return false;
    }

    const trimmedSku = sku.trim();

    // Check length
    if (trimmedSku.length < SKU.MIN_LENGTH || trimmedSku.length > SKU.MAX_LENGTH) {
      return false;
    }

    // Check pattern (alphanumeric, hyphens, underscores only)
    if (!SKU.VALID_PATTERN.test(trimmedSku)) {
      return false;
    }

    // Check for consecutive delimiters
    if (trimmedSku.includes('--') || trimmedSku.includes('__')) {
      return false;
    }

    // Check for leading/trailing delimiters
    if (
      trimmedSku.startsWith('-') ||
      trimmedSku.endsWith('-') ||
      trimmedSku.startsWith('_') ||
      trimmedSku.endsWith('_')
    ) {
      return false;
    }

    return true;
  }

  /**
   * Generate SKU from parts
   */
  static fromParts(parts: SKUParts): SKU {
    const skuParts: string[] = [];

    if (parts.style) {
      skuParts.push(parts.style.toUpperCase());
    }
    if (parts.color) {
      skuParts.push(parts.color.toUpperCase());
    }
    if (parts.size) {
      skuParts.push(parts.size.toString());
    }
    if (parts.design) {
      skuParts.push(parts.design.toUpperCase());
    }

    if (skuParts.length === 0) {
      throw new Error('At least one SKU part is required');
    }

    return new SKU(skuParts.join('-'));
  }

  /**
   * Generate random SKU
   */
  static generateRandom(prefix?: string, length: number = 8): SKU {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix ? `${prefix.toUpperCase()}-` : '';

    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    return new SKU(result);
  }

  /**
   * Create SKU from string
   */
  static create(sku: string): SKU {
    return new SKU(sku);
  }

  /**
   * Convert to string
   */
  toString(): string {
    return this.props.value;
  }
}
