import { ValueObject } from '@domain/common/value-object';

interface QuantityProps {
  value: number;
}

export class Quantity extends ValueObject<QuantityProps> {
  private static readonly MIN_VALUE = 0;
  private static readonly MAX_VALUE = 999999;

  constructor(quantity: number) {
    if (!Quantity.isValid(quantity)) {
      throw new Error(`Invalid quantity: ${quantity}`);
    }

    super({ value: Math.floor(quantity) }); // Ensure integer
  }

  /**
   * Get quantity value
   */
  get quantityValue(): number {
    return this.props.value;
  }

  /**
   * Check if quantity is zero
   */
  get isZero(): boolean {
    return this.props.value === 0;
  }

  /**
   * Check if quantity is positive
   */
  get isPositive(): boolean {
    return this.props.value > 0;
  }

  /**
   * Check if quantity is low (less than 10)
   */
  get isLow(): boolean {
    return this.props.value > 0 && this.props.value < 10;
  }

  /**
   * Check if quantity is out of stock
   */
  get isOutOfStock(): boolean {
    return this.props.value === 0;
  }

  /**
   * Check if quantity is in stock
   */
  get isInStock(): boolean {
    return this.props.value > 0;
  }

  /**
   * Add to quantity
   */
  add(amount: number): Quantity {
    if (!Number.isInteger(amount) || amount < 0) {
      throw new Error(`Invalid amount to add: ${amount}`);
    }

    return new Quantity(this.props.value + amount);
  }

  /**
   * Subtract from quantity
   */
  subtract(amount: number): Quantity {
    if (!Number.isInteger(amount) || amount < 0) {
      throw new Error(`Invalid amount to subtract: ${amount}`);
    }

    const newValue = Math.max(0, this.props.value - amount);
    return new Quantity(newValue);
  }

  /**
   * Set new quantity
   */
  set(newQuantity: number): Quantity {
    return new Quantity(newQuantity);
  }

  /**
   * Increase by one
   */
  increment(): Quantity {
    return this.add(1);
  }

  /**
   * Decrease by one
   */
  decrement(): Quantity {
    return this.subtract(1);
  }

  /**
   * Compare with another quantity
   */
  compare(other: Quantity): number {
    if (this.props.value < other.props.value) return -1;
    if (this.props.value > other.props.value) return 1;
    return 0;
  }

  /**
   * Check if greater than another quantity
   */
  isGreaterThan(other: Quantity): boolean {
    return this.compare(other) > 0;
  }

  /**
   * Check if less than another quantity
   */
  isLessThan(other: Quantity): boolean {
    return this.compare(other) < 0;
  }

  /**
   * Check if equal to another quantity
   */
  isEqualTo(other: Quantity): boolean {
    return this.compare(other) === 0;
  }

  /**
   * Check if sufficient for requested amount
   */
  isSufficientFor(requestedAmount: number): boolean {
    return this.props.value >= requestedAmount;
  }

  /**
   * Get shortage amount if insufficient
   */
  getShortage(requestedAmount: number): number {
    return Math.max(0, requestedAmount - this.props.value);
  }

  /**
   * Get available amount for allocation
   */
  getAvailable(): number {
    return this.props.value;
  }

  /**
   * Allocate quantity (returns allocated amount and remaining)
   */
  allocate(requestedAmount: number): { allocated: number; remaining: Quantity } {
    if (!Number.isInteger(requestedAmount) || requestedAmount < 0) {
      throw new Error(`Invalid requested amount: ${requestedAmount}`);
    }

    const allocated = Math.min(this.props.value, requestedAmount);
    const remaining = new Quantity(this.props.value - allocated);

    return { allocated, remaining };
  }

  /**
   * Format quantity for display
   */
  format(): string {
    return this.props.value.toLocaleString();
  }

  /**
   * Get stock status description
   */
  getStockStatus(): 'out_of_stock' | 'low_stock' | 'in_stock' {
    if (this.isOutOfStock) return 'out_of_stock';
    if (this.isLow) return 'low_stock';
    return 'in_stock';
  }

  /**
   * Get stock level percentage (based on max stock level)
   */
  getStockLevelPercentage(maxStock: number): number {
    if (maxStock <= 0) return 0;
    return Math.min(100, (this.props.value / maxStock) * 100);
  }

  /**
   * Validate quantity value
   */
  static isValid(quantity: number): boolean {
    return (
      typeof quantity === 'number' &&
      Number.isInteger(quantity) &&
      quantity >= Quantity.MIN_VALUE &&
      quantity <= Quantity.MAX_VALUE
    );
  }

  /**
   * Create zero quantity
   */
  static zero(): Quantity {
    return new Quantity(0);
  }

  /**
   * Create quantity from string
   */
  static fromString(quantityString: string): Quantity {
    const quantity = parseInt(quantityString, 10);
    if (isNaN(quantity)) {
      throw new Error(`Invalid quantity string: ${quantityString}`);
    }
    return new Quantity(quantity);
  }

  /**
   * Get maximum allowed quantity
   */
  static getMaxValue(): number {
    return Quantity.MAX_VALUE;
  }

  /**
   * Get minimum allowed quantity
   */
  static getMinValue(): number {
    return Quantity.MIN_VALUE;
  }

  /**
   * Convert to string
   */
  toString(): string {
    return this.props.value.toString();
  }
}
