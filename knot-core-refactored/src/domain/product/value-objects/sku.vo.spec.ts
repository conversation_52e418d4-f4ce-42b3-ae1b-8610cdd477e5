import { SKU } from './sku.vo';

describe('SKU Value Object', () => {
  describe('creation', () => {
    it('should create a valid SKU', () => {
      const sku = new SKU('TEST-SKU-001');
      expect(sku.skuValue).toBe('TEST-SKU-001');
    });

    it('should convert to uppercase and trim', () => {
      const sku = new SKU('  test-sku-001  ');
      expect(sku.skuValue).toBe('TEST-SKU-001');
    });

    it('should throw error for empty SKU', () => {
      expect(() => new SKU('')).toThrow('Invalid SKU format');
    });

    it('should throw error for invalid characters', () => {
      expect(() => new SKU('TEST@SKU')).toThrow('Invalid SKU format');
    });
  });

  describe('parsing', () => {
    it('should extract SKU parts correctly', () => {
      const sku = new SKU('STYLE-COLOR-12-DESIGN');
      const parts = sku.extractParts();

      expect(parts.style).toBe('STYLE');
      expect(parts.color).toBe('COLOR');
      expect(parts.size).toBe(12);
      expect(parts.design).toBe('DESIGN');
    });
  });

  describe('validation', () => {
    it('should validate correct SKU format', () => {
      expect(SKU.isValid('TEST-SKU-001')).toBe(true);
    });

    it('should reject invalid SKU format', () => {
      expect(SKU.isValid('')).toBe(false);
      expect(SKU.isValid('TEST@SKU')).toBe(false);
    });
  });

  describe('equality', () => {
    it('should be equal for same SKU values', () => {
      const sku1 = new SKU('TEST-SKU-001');
      const sku2 = new SKU('test-sku-001');

      expect(sku1.equals(sku2)).toBe(true);
    });

    it('should not be equal for different SKU values', () => {
      const sku1 = new SKU('TEST-SKU-001');
      const sku2 = new SKU('TEST-SKU-002');

      expect(sku1.equals(sku2)).toBe(false);
    });
  });
});
