import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>To<PERSON>ne, Join<PERSON>olumn, Index } from 'typeorm';
import { BaseEntity } from '@domain/common/base.entity';
import { Product } from './product.entity';
import { Quantity } from './value-objects/quantity.vo';

@Entity('product_designs')
@Index(['productId', 'title'])
export class ProductDesign extends BaseEntity {
  @Column({ type: 'varchar', length: 255, default: '' })
  title!: string;

  @Column({ type: 'int', default: 0 })
  quantity!: number;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imageUrl?: string;

  @Column({ type: 'boolean', default: true })
  isActive!: boolean;

  @Column({ type: 'int', default: 0 })
  sortOrder!: number;

  @Column({ type: 'uuid' })
  productId!: string;

  @ManyToOne(() => Product, (product) => product.designs, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'productId' })
  product!: Product;

  /**
   * Check if design is available
   */
  get isAvailable(): boolean {
    return this.isActive && this.quantity > 0;
  }

  /**
   * Check if design is out of stock
   */
  get isOutOfStock(): boolean {
    return this.quantity <= 0;
  }

  /**
   * Check if design is low stock (less than 5 units)
   */
  get isLowStock(): boolean {
    return this.quantity > 0 && this.quantity < 5;
  }

  /**
   * Update quantity
   */
  updateQuantity(quantity: number): void {
    this.quantity = Math.max(0, quantity);
  }

  /**
   * Add to quantity
   */
  addQuantity(amount: number): void {
    this.quantity = Math.max(0, this.quantity + amount);
  }

  /**
   * Subtract from quantity
   */
  subtractQuantity(amount: number): void {
    this.quantity = Math.max(0, this.quantity - amount);
  }

  /**
   * Activate design
   */
  activate(): void {
    this.isActive = true;
  }

  /**
   * Deactivate design
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * Update basic information
   */
  updateInfo(data: {
    title?: string;
    description?: string;
    imageUrl?: string;
    sortOrder?: number;
  }): void {
    if (data.title !== undefined) this.title = data.title;
    if (data.description !== undefined) this.description = data.description;
    if (data.imageUrl !== undefined) this.imageUrl = data.imageUrl;
    if (data.sortOrder !== undefined) this.sortOrder = data.sortOrder;
  }

  /**
   * Get quantity value object
   */
  getQuantityVO(): Quantity {
    return new Quantity(this.quantity);
  }

  /**
   * Check if design has image
   */
  hasImage(): boolean {
    return !!this.imageUrl;
  }

  /**
   * Get display title (fallback to 'Untitled' if empty)
   */
  getDisplayTitle(): string {
    return this.title || 'Untitled Design';
  }
}
