import {
  PrimaryGeneratedColumn,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  DeleteDateColumn,
  VersionColumn,
} from 'typeorm';

export abstract class BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @CreateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
  })
  createdAt!: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP(6)',
    onUpdate: 'CURRENT_TIMESTAMP(6)',
  })
  updatedAt!: Date;

  @DeleteDateColumn({
    type: 'timestamp',
    nullable: true,
  })
  deletedAt?: Date;

  @VersionColumn()
  version!: number;

  /**
   * Check if entity is soft deleted
   */
  get isDeleted(): boolean {
    return this.deletedAt !== null && this.deletedAt !== undefined;
  }

  /**
   * Get entity age in milliseconds
   */
  get age(): number {
    return Date.now() - this.createdAt.getTime();
  }

  /**
   * Check if entity was recently created (within last hour)
   */
  get isRecentlyCreated(): boolean {
    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
    return this.age < oneHour;
  }

  /**
   * Check if entity was recently updated (within last hour)
   */
  get isRecentlyUpdated(): boolean {
    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
    return Date.now() - this.updatedAt.getTime() < oneHour;
  }

  /**
   * Soft delete the entity
   */
  softDelete(): void {
    this.deletedAt = new Date();
  }

  /**
   * Restore soft deleted entity
   */
  restore(): void {
    this.deletedAt = null;
  }

  /**
   * Convert entity to plain object
   */
  toJSON(): Record<string, any> {
    const result: Record<string, any> = {};

    for (const key in this) {
      if (this.hasOwnProperty(key)) {
        const value = this[key];
        if (value instanceof Date) {
          result[key] = value.toISOString();
        } else if (typeof value !== 'function') {
          result[key] = value;
        }
      }
    }

    return result;
  }

  /**
   * Check if two entities are equal (by ID)
   */
  equals(other: BaseEntity): boolean {
    return this.id === other.id;
  }

  /**
   * Clone entity (without ID and timestamps)
   */
  clone(): Partial<this> {
    const cloned = { ...this };
    delete (cloned as any).id;
    delete (cloned as any).createdAt;
    delete (cloned as any).updatedAt;
    delete (cloned as any).deletedAt;
    delete (cloned as any).version;
    return cloned;
  }
}
