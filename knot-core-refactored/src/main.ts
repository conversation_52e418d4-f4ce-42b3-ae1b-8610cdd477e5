import { NestFactory } from '@nestjs/core';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import helmet from 'helmet';
import * as compression from 'compression';

import { AppModule } from './app.module';
import { HttpExceptionFilter } from '@shared/filters/http-exception.filter';
import { TransformInterceptor } from '@shared/interceptors/transform.interceptor';
import { LoggingInterceptor } from '@shared/interceptors/logging.interceptor';

async function bootstrap(): Promise<void> {
  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
  });

  // Get configuration service
  const configService = app.get(ConfigService);
  const port = configService.get<number>('PORT', 3000);
  const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // Use default logger for now
  // app.useLogger(app.get(Logger));

  // Security middleware
  app.use(helmet());
  // app.use(compression()); // temporarily disabled

  // CORS configuration
  app.enableCors({
    origin: nodeEnv === 'production' ? false : true,
    credentials: true,
  });

  // Global prefix
  app.setGlobalPrefix('api');

  // API versioning
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  // Global pipes
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Global filters
  app.useGlobalFilters(new HttpExceptionFilter());

  // Global interceptors
  app.useGlobalInterceptors(new TransformInterceptor(), new LoggingInterceptor());

  // Swagger documentation
  if (nodeEnv !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('Knot Theory Rings - Core API')
      .setDescription(
        `
        ## Knot Theory Rings E-commerce Platform Integration API

        This API provides comprehensive order management and platform integration capabilities for Knot Theory Rings.

        ### Features:
        - 🔐 **JWT Authentication** - Secure user authentication and authorization
        - 📦 **Order Management** - Complete order processing with filtering and pagination
        - 📊 **Analytics** - Order statistics and reporting
        - 🔗 **Platform Integration** - Shopify, Etsy, and Amazon marketplace integration
        - 🏥 **Health Monitoring** - Application health checks and database seeding

        ### Getting Started:
        1. **Register a user** using \`POST /auth/register\`
        2. **Login** using \`POST /auth/login\` to get your JWT token
        3. **Authorize** by clicking the 🔒 button and entering: \`Bearer YOUR_JWT_TOKEN\`
        4. **Explore** the order endpoints with full authentication

        ### Authentication:
        All order endpoints require a valid JWT token. Use the "Authorize" button above to set your token.
      `,
      )
      .setVersion('2.0.0')
      .setContact('Knot Theory Rings', 'https://knotheoryrings.com', '<EMAIL>')
      .setLicense('MIT', 'https://opensource.org/licenses/MIT')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addTag('Authentication', 'User authentication and authorization endpoints')
      .addTag('Orders', 'Order management and processing endpoints')
      .addTag('Health', 'Application health checks and utilities')
      .addServer('http://localhost:3001', 'Local Development Server')
      .addServer('http://localhost:3001', 'Docker Development Server')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        docExpansion: 'none',
        filter: true,
        showRequestDuration: true,
        tryItOutEnabled: true,
        requestInterceptor: (req: any) => {
          req.headers['Content-Type'] = 'application/json';
          return req;
        },
      },
      customSiteTitle: 'Knot Theory Rings API Documentation',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #3b82f6; }
        .swagger-ui .scheme-container { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
      `,
    });
  }

  // Graceful shutdown
  app.enableShutdownHooks();

  await app.listen(port);

  console.log(`🚀 Application is running on: http://localhost:${port}/api`);

  if (nodeEnv !== 'production') {
    console.log(`📚 Swagger documentation: http://localhost:${port}/api/docs`);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

bootstrap().catch(error => {
  console.error('Failed to start application:', error);
  process.exit(1);
});
