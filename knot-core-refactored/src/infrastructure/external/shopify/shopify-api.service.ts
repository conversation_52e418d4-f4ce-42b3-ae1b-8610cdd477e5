import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService, ApiConfig } from '../base-api.service';
import { TokenManagerService } from '../../../shared/services/token-manager.service';

// Import interfaces and types
import { ShopifyProduct, ShopifyVariant, ShopifyOrder, ShopifyInventoryLevel } from './interfaces';
import {
  GetProductsParams,
  GetOrdersParams,
  GetAllOrdersParams,
  GetInventoryLevelsParams,
  GetInventoryItemsParams,
  CreateWebhookParams,
  OrdersResponse,
} from './types';

// Import services
import {
  ShopifyAuthService,
  ShopifyProductsService,
  ShopifyOrdersService,
  ShopifyInventoryService,
  ShopifyWebhooksService,
} from './services';

// Re-export interfaces for backward compatibility
export * from './interfaces';
export * from './types';

@Injectable()
export class ShopifyApiService extends BaseApiService implements OnModuleInit {
  private authService: ShopifyAuthService;
  private productsService: ShopifyProductsService;
  private ordersService: ShopifyOrdersService;
  private inventoryService: ShopifyInventoryService;
  private webhooksService: ShopifyWebhooksService;

  constructor(
    private readonly configService: ConfigService,
    private readonly tokenManager: TokenManagerService,
  ) {
    const shopDomain = configService.get<string>('SHOPIFY_SHOP_DOMAIN', '');
    const accessToken = configService.get<string>('SHOPIFY_ACCESS_TOKEN', '');
    const apiVersion = configService.get<string>('SHOPIFY_API_VERSION', '2023-10');

    const config: ApiConfig = {
      baseURL: `https://${shopDomain}/admin/api/${apiVersion}`,
      timeout: 30000,
      retries: 3,
      retryDelay: 2000,
      headers: {
        'X-Shopify-Access-Token': accessToken,
      },
    };

    super(config);

    // Initialize specialized services
    this.authService = new ShopifyAuthService(configService, tokenManager, this);
    this.productsService = new ShopifyProductsService(this);
    this.ordersService = new ShopifyOrdersService(this);
    this.inventoryService = new ShopifyInventoryService(this);
    this.webhooksService = new ShopifyWebhooksService(this);
  }

  async onModuleInit() {
    await this.authService.initialize();
  }

  // ===== AUTHENTICATION METHODS =====

  /**
   * Health check for Shopify API
   */
  async healthCheck(): Promise<boolean> {
    return this.authService.healthCheck();
  }

  /**
   * Get shop information
   */
  async getShop(): Promise<any> {
    return this.authService.getShop();
  }

  /**
   * Set access token
   */
  setAccessToken(token: string): void {
    this.authService.setAccessToken(token);
  }

  /**
   * Remove access token
   */
  removeAccessToken(): void {
    this.authService.removeAccessToken();
  }

  /**
   * Store new OAuth tokens (for manual token updates)
   */
  async storeTokens(tokenData: {
    accessToken: string;
    expiresAt?: Date;
    scopes?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    return this.authService.storeTokens(tokenData);
  }

  /**
   * Get token status
   */
  async getTokenStatus(): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    return this.authService.getTokenStatus();
  }

  /**
   * Revoke stored tokens
   */
  async revokeTokens(): Promise<void> {
    return this.authService.revokeTokens();
  }

  // ===== PRODUCT METHODS =====

  /**
   * Get products
   */
  async getProducts(params: GetProductsParams = {}): Promise<ShopifyProduct[]> {
    return this.productsService.getProducts(params);
  }

  /**
   * Get product by ID
   */
  async getProduct(productId: number, fields?: string): Promise<ShopifyProduct> {
    return this.productsService.getProduct(productId, fields);
  }

  /**
   * Update product
   */
  async updateProduct(
    productId: number,
    product: Partial<ShopifyProduct>,
  ): Promise<ShopifyProduct> {
    return this.productsService.updateProduct(productId, product);
  }

  /**
   * Get product variants
   */
  async getProductVariants(productId: number): Promise<ShopifyVariant[]> {
    return this.productsService.getProductVariants(productId);
  }

  /**
   * Get variant by ID
   */
  async getVariant(variantId: number): Promise<ShopifyVariant> {
    return this.productsService.getVariant(variantId);
  }

  /**
   * Update variant
   */
  async updateVariant(
    variantId: number,
    variant: Partial<ShopifyVariant>,
  ): Promise<ShopifyVariant> {
    return this.productsService.updateVariant(variantId, variant);
  }

  // ===== ORDER METHODS =====

  /**
   * Get orders (single page)
   */
  async getOrders(params: GetOrdersParams = {}): Promise<OrdersResponse> {
    return this.ordersService.getOrders(params);
  }

  /**
   * Get ALL orders with automatic pagination
   */
  async getAllOrders(params: GetAllOrdersParams = {}): Promise<ShopifyOrder[]> {
    return this.ordersService.getAllOrders(params);
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId: number, fields?: string): Promise<ShopifyOrder> {
    return this.ordersService.getOrder(orderId, fields);
  }

  // ===== INVENTORY METHODS =====

  /**
   * Get inventory levels
   */
  async getInventoryLevels(params: GetInventoryLevelsParams): Promise<ShopifyInventoryLevel[]> {
    return this.inventoryService.getInventoryLevels(params);
  }

  /**
   * Update inventory level
   */
  async updateInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    available: number,
  ): Promise<ShopifyInventoryLevel> {
    return this.inventoryService.updateInventoryLevel(inventoryItemId, locationId, available);
  }

  /**
   * Adjust inventory level
   */
  async adjustInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    quantity: number,
  ): Promise<ShopifyInventoryLevel> {
    return this.inventoryService.adjustInventoryLevel(inventoryItemId, locationId, quantity);
  }

  /**
   * Get locations
   */
  async getLocations(): Promise<any[]> {
    return this.inventoryService.getLocations();
  }

  /**
   * Get inventory items
   */
  async getInventoryItems(params: GetInventoryItemsParams = {}): Promise<any[]> {
    return this.inventoryService.getInventoryItems(params);
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryItem(inventoryItemId: number): Promise<any> {
    return this.inventoryService.getInventoryItem(inventoryItemId);
  }

  /**
   * Update inventory item
   */
  async updateInventoryItem(inventoryItemId: number, inventoryItem: any): Promise<any> {
    return this.inventoryService.updateInventoryItem(inventoryItemId, inventoryItem);
  }

  /**
   * Get webhooks
   */
  async getWebhooks(): Promise<any[]> {
    const response = await this.get('/webhooks.json');
    return response.data.webhooks || [];
  }

  /**
   * Create webhook
   */
  async createWebhook(webhook: {
    topic: string;
    address: string;
    format?: 'json' | 'xml';
    fields?: string[];
  }): Promise<any> {
    const response = await this.post('/webhooks.json', { webhook });
    return response.data.webhook;
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId: number): Promise<boolean> {
    try {
      await this.delete(`/webhooks/${webhookId}.json`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete webhook ${webhookId}:`, error);
      return false;
    }
  }

  /**
   * Set access token
   */
  setAccessToken(token: string): void {
    this.setHeader('X-Shopify-Access-Token', token);
  }

  /**
   * Remove access token
   */
  removeAccessToken(): void {
    this.removeHeader('X-Shopify-Access-Token');
  }

  /**
   * Store new OAuth tokens (for manual token updates)
   */
  async storeTokens(tokenData: {
    accessToken: string;
    expiresAt?: Date;
    scopes?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.tokenManager.storeTokens(TokenProvider.SHOPIFY, {
      ...tokenData,
      refreshToken: '', // Shopify doesn't use refresh tokens
      shopId: this.shopDomain,
      shopName: this.shopDomain,
    });

    // Update current authentication
    await this.setupAuthentication();
  }

  /**
   * Get token status
   */
  async getTokenStatus(): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    return this.tokenManager.getTokenStatus(TokenProvider.SHOPIFY, this.shopDomain);
  }

  /**
   * Revoke stored tokens
   */
  async revokeTokens(): Promise<void> {
    await this.tokenManager.revokeTokens(TokenProvider.SHOPIFY, this.shopDomain);
    this.removeAccessToken();
  }
}
