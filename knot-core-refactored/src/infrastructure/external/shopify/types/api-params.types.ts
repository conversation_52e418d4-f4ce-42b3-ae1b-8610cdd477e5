export interface GetProductsParams {
  ids?: string;
  limit?: number;
  since_id?: number;
  title?: string;
  vendor?: string;
  handle?: string;
  product_type?: string;
  status?: 'active' | 'archived' | 'draft';
  created_at_min?: string;
  created_at_max?: string;
  updated_at_min?: string;
  updated_at_max?: string;
  published_at_min?: string;
  published_at_max?: string;
  published_status?: 'published' | 'unpublished' | 'any';
  fields?: string;
}

export interface GetOrdersParams {
  ids?: string;
  limit?: number;
  since_id?: number;
  created_at_min?: string;
  created_at_max?: string;
  updated_at_min?: string;
  updated_at_max?: string;
  processed_at_min?: string;
  processed_at_max?: string;
  attribution_app_id?: string;
  status?: 'open' | 'closed' | 'cancelled' | 'any';
  financial_status?:
    | 'authorized'
    | 'pending'
    | 'paid'
    | 'partially_paid'
    | 'refunded'
    | 'voided'
    | 'partially_refunded'
    | 'any'
    | 'unpaid';
  fulfillment_status?: 'shipped' | 'partial' | 'unshipped' | 'any' | 'unfulfilled';
  fields?: string;
  page_info?: string;
}

export interface GetAllOrdersParams extends Omit<GetOrdersParams, 'page_info'> {
  maxPages?: number; // Safety limit to prevent infinite loops
}

export interface GetInventoryLevelsParams {
  inventory_item_ids?: string;
  location_ids?: string;
  limit?: number;
  updated_at_min?: string;
}

export interface GetInventoryItemsParams {
  ids?: string;
  limit?: number;
}

export interface CreateWebhookParams {
  topic: string;
  address: string;
  format?: 'json' | 'xml';
  fields?: string[];
}

export interface PageInfo {
  hasNext: boolean;
  hasPrevious: boolean;
  nextPageInfo?: string;
  previousPageInfo?: string;
}

// Import ShopifyOrder for the backward compatibility interface
import { ShopifyOrder } from '../interfaces';

export interface PaginatedResponse<T> {
  data: T[];
  pageInfo?: PageInfo;
}

// For backward compatibility with existing code
export interface OrdersResponse {
  orders: ShopifyOrder[];
  pageInfo?: PageInfo;
}
