import { Injectable, Logger } from '@nestjs/common';
import { BaseApiService } from '../../base-api.service';
import { CreateWebhookParams } from '../types';

@Injectable()
export class ShopifyWebhooksService {
  private readonly logger = new Logger(ShopifyWebhooksService.name);

  constructor(private readonly apiService: BaseApiService) {}

  /**
   * Get webhooks
   */
  async getWebhooks(): Promise<any[]> {
    const response = await this.apiService.get('/webhooks.json');
    return response.data.webhooks || [];
  }

  /**
   * Create webhook
   */
  async createWebhook(webhook: CreateWebhookParams): Promise<any> {
    const response = await this.apiService.post('/webhooks.json', { webhook });
    return response.data.webhook;
  }

  /**
   * Delete webhook
   */
  async deleteWebhook(webhookId: number): Promise<boolean> {
    try {
      await this.apiService.delete(`/webhooks/${webhookId}.json`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete webhook ${webhookId}:`, error);
      return false;
    }
  }
}
