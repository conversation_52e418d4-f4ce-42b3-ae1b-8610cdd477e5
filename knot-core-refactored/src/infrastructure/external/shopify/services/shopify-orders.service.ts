import { Injectable, Logger } from '@nestjs/common';
import { BaseApiService } from '../../base-api.service';
import { ShopifyOrder } from '../interfaces';
import { GetOrdersParams, GetAllOrdersParams, OrdersResponse } from '../types';
import { parseLinkHeader } from '../utils';

@Injectable()
export class ShopifyOrdersService {
  private readonly logger = new Logger(ShopifyOrdersService.name);

  constructor(private readonly apiService: BaseApiService) {}

  /**
   * Get orders (single page)
   */
  async getOrders(params: GetOrdersParams = {}): Promise<OrdersResponse> {
    const response = await this.apiService.get('/orders.json', { params });

    // Parse Link header for pagination info
    const linkHeader = response.headers.link;
    const pageInfo = parseLinkHeader(linkHeader);

    return {
      orders: response.data.orders || [],
      pageInfo,
    };
  }

  /**
   * Get ALL orders with automatic pagination
   */
  async getAllOrders(params: GetAllOrdersParams = {}): Promise<ShopifyOrder[]> {
    const allOrders: ShopifyOrder[] = [];
    const maxPages = params.maxPages || 100; // Default safety limit
    let currentPage = 0;
    let nextPageInfo: string | undefined;

    // Remove maxPages from params as it's not a Shopify API parameter
    const { maxPages: _, ...apiParams } = params;

    do {
      currentPage++;

      // For subsequent pages, only use page_info, limit, and fields
      const requestParams = nextPageInfo
        ? {
            page_info: nextPageInfo,
            limit: apiParams.limit || 250,
            ...(apiParams.fields && { fields: apiParams.fields }),
          }
        : apiParams;

      this.logger.debug(`Fetching orders page ${currentPage}`, {
        hasPageInfo: !!nextPageInfo,
        limit: requestParams.limit,
      });

      const result = await this.getOrders(requestParams);

      if (result.data.length > 0) {
        allOrders.push(...result.data);
        this.logger.debug(`Retrieved ${result.data.length} orders (total: ${allOrders.length})`);
      }

      nextPageInfo = result.pageInfo?.nextPageInfo;

      // Safety checks
      if (currentPage >= maxPages) {
        this.logger.warn(`Reached maximum page limit (${maxPages}), stopping pagination`);
        break;
      }
    } while (nextPageInfo);

    this.logger.log(
      `Completed pagination: ${allOrders.length} total orders across ${currentPage} pages`,
    );
    return allOrders;
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId: number, fields?: string): Promise<ShopifyOrder> {
    const params = fields ? { fields } : {};
    const response = await this.apiService.get(`/orders/${orderId}.json`, { params });
    return response.data.order;
  }
}
