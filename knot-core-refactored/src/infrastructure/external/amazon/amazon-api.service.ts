import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { BaseApiService, ApiConfig } from '../base-api.service';
import { TokenManagerService } from '../../../shared/services/token-manager.service';
import { TokenProvider } from '../../../domain/common/entities/oauth-token.entity';

export interface AmazonProduct {
  asin: string;
  sku: string;
  fnsku?: string;
  title: string;
  quantity: number;
  price?: number;
  currency?: string;
  status: string;
  lastUpdated: Date;
}

export interface AmazonOrder {
  orderId: string;
  orderStatus: string;
  purchaseDate: Date;
  lastUpdateDate: Date;
  fulfillmentChannel: string;
  salesChannel: string;
  orderTotal: {
    amount: number;
    currencyCode: string;
  };
  numberOfItemsShipped: number;
  numberOfItemsUnshipped: number;
  paymentExecutionDetail?: any;
  paymentMethod: string;
  marketplaceId: string;
  shipmentServiceLevelCategory: string;
  orderType: string;
  earliestShipDate?: Date;
  latestShipDate?: Date;
  earliestDeliveryDate?: Date;
  latestDeliveryDate?: Date;
  isBusinessOrder: boolean;
  isPrime: boolean;
  isPremiumOrder: boolean;
  isGlobalExpressEnabled: boolean;
  replacedOrderId?: string;
  isReplacementOrder: boolean;
  promiseResponseDueDate?: Date;
  isEstimatedShipDateSet: boolean;
  isSoldByAB: boolean;
  isIBA: boolean;
  defaultShipFromLocationAddress?: any;
  buyerInvoicePreference?: string;
  buyerTaxInformation?: any;
  fulfillmentInstruction?: any;
  isISPU: boolean;
  isAccessPointOrder: boolean;
  marketplaceTaxInfo?: any;
  sellerDisplayName?: string;
}

export interface AmazonOrderItem {
  asin: string;
  sellerSku: string;
  orderItemId: string;
  title: string;
  quantityOrdered: number;
  quantityShipped: number;
  productInfo?: any;
  pointsGranted?: any;
  itemPrice?: {
    amount: number;
    currencyCode: string;
  };
  shippingPrice?: {
    amount: number;
    currencyCode: string;
  };
  itemTax?: {
    amount: number;
    currencyCode: string;
  };
  shippingTax?: {
    amount: number;
    currencyCode: string;
  };
  shippingDiscount?: {
    amount: number;
    currencyCode: string;
  };
  shippingDiscountTax?: {
    amount: number;
    currencyCode: string;
  };
  promotionDiscount?: {
    amount: number;
    currencyCode: string;
  };
  promotionDiscountTax?: {
    amount: number;
    currencyCode: string;
  };
  promotionIds?: string[];
  codFee?: {
    amount: number;
    currencyCode: string;
  };
  codFeeDiscount?: {
    amount: number;
    currencyCode: string;
  };
  isGift: boolean;
  conditionNote?: string;
  conditionId?: string;
  conditionSubtypeId?: string;
  scheduledDeliveryStartDate?: Date;
  scheduledDeliveryEndDate?: Date;
  priceDesignation?: string;
  taxCollection?: any;
  serialNumberRequired: boolean;
  isTransparency: boolean;
  iossNumber?: string;
  storeChainStoreId?: string;
  deemedResellerCategory?: string;
  buyerInfo?: any;
  buyerRequestedCancel?: any;
}

export interface AmazonInventoryItem {
  sellerSku: string;
  fnSku: string;
  asin: string;
  condition: string;
  totalQuantity: number;
  inStockQuantity: number;
  reservedQuantity: number;
  fulfillableQuantity: number;
  inboundWorkingQuantity: number;
  inboundShippedQuantity: number;
  inboundReceivingQuantity: number;
  researchingQuantity: number;
  unfulfillableQuantity: number;
  lastUpdatedTime: Date;
  productName: string;
}

@Injectable()
export class AmazonApiService extends BaseApiService implements OnModuleInit {
  private readonly accessKeyId: string;
  private readonly secretAccessKey: string;
  private readonly region: string;
  private readonly marketplaceId: string;
  private readonly sellerId: string;
  private readonly accessToken: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly tokenManager: TokenManagerService,
  ) {
    const config: ApiConfig = {
      baseURL: 'https://sellingpartnerapi-na.amazon.com',
      timeout: 30000,
      retries: 3,
      retryDelay: 2000,
      headers: {
        'x-amz-access-token': '', // Will be set dynamically
      },
    };

    super(config);

    this.accessKeyId = this.configService.get<string>('AMAZON_ACCESS_KEY_ID', '');
    this.secretAccessKey = this.configService.get<string>('AMAZON_SECRET_ACCESS_KEY', '');
    this.region = this.configService.get<string>('AMAZON_REGION', 'us-east-1');
    this.marketplaceId = this.configService.get<string>('AMAZON_MARKETPLACE_ID', '');
    this.sellerId = this.configService.get<string>('AMAZON_SELLER_ID', '');
    this.accessToken = this.configService.get<string>('AMAZON_ACCESS_TOKEN', '');
  }

  async onModuleInit() {
    // Register the refresh function with the token manager (Amazon uses LWA tokens)
    this.tokenManager.registerRefreshFunction(
      TokenProvider.AMAZON,
      async (refreshToken: string) => {
        return this.performTokenRefresh(refreshToken);
      },
    );

    // Migrate existing tokens from environment to secure storage
    await this.migrateExistingTokens();

    // Set up authentication with token manager
    await this.setupAuthentication();
  }

  /**
   * Migrate existing tokens from environment to secure storage
   */
  private async migrateExistingTokens(): Promise<void> {
    if (this.accessToken && this.sellerId) {
      try {
        const tokenStatus = await this.tokenManager.getTokenStatus(
          TokenProvider.AMAZON,
          this.sellerId,
        );
        if (!tokenStatus.hasToken) {
          this.logger.log('Migrating existing Amazon tokens to secure storage...');

          await this.tokenManager.storeTokens(TokenProvider.AMAZON, {
            accessToken: this.accessToken,
            refreshToken: '', // Amazon refresh tokens are handled separately
            shopId: this.sellerId,
            shopName: this.sellerId,
            scopes: 'sellingpartnerapi::notifications',
            metadata: {
              migratedFromEnv: true,
              migratedAt: new Date().toISOString(),
              marketplaceId: this.marketplaceId,
              region: this.region,
            },
          });

          this.logger.log('Successfully migrated Amazon tokens to secure storage');
        }
      } catch (error) {
        this.logger.error('Failed to migrate existing tokens:', error);
      }
    }
  }

  /**
   * Set up authentication using token manager
   */
  private async setupAuthentication(): Promise<void> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.AMAZON,
        this.sellerId,
      );
      if (accessToken) {
        this.setAccessToken(accessToken);
        this.logger.log('Successfully set up Amazon authentication from token manager');
      } else {
        this.logger.warn('No valid Amazon access token available');
      }
    } catch (error) {
      this.logger.error('Failed to set up authentication:', error);
    }
  }

  /**
   * Ensure we have a valid access token before making API calls
   */
  private async ensureValidToken(): Promise<boolean> {
    try {
      const accessToken = await this.tokenManager.getValidAccessToken(
        TokenProvider.AMAZON,
        this.sellerId,
      );
      if (accessToken) {
        this.setAccessToken(accessToken);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to ensure valid token:', error);
      return false;
    }
  }

  /**
   * Perform token refresh (used by token manager)
   */
  private async performTokenRefresh(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
  }> {
    try {
      this.logger.log('Refreshing Amazon access token...');

      // Amazon LWA token refresh implementation
      // This would need to be implemented based on Amazon's LWA documentation
      throw new Error('Amazon token refresh not implemented. Please update tokens manually.');
    } catch (error) {
      this.logger.error('Failed to refresh Amazon token:', error);
      throw error;
    }
  }

  /**
   * Health check for Amazon API
   */
  async healthCheck(): Promise<boolean> {
    try {
      // Ensure we have a valid token before making the health check
      const hasValidToken = await this.ensureValidToken();
      if (!hasValidToken) {
        this.logger.warn('No valid Amazon access token available for health check');
        return false;
      }

      // Simple API call to check connectivity
      await this.get('/orders/v0/orders', {
        params: {
          MarketplaceIds: this.marketplaceId,
          CreatedAfter: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          MaxResultsPerPage: 1,
        },
      });
      return true;
    } catch (error) {
      this.logger.error('Amazon API health check failed:', error);
      return false;
    }
  }

  /**
   * Get orders from Amazon
   */
  async getOrders(params: {
    createdAfter?: Date;
    createdBefore?: Date;
    lastUpdatedAfter?: Date;
    lastUpdatedBefore?: Date;
    orderStatuses?: string[];
    fulfillmentChannels?: string[];
    paymentMethods?: string[];
    buyerEmail?: string;
    sellerOrderId?: string;
    maxResultsPerPage?: number;
    nextToken?: string;
  }): Promise<{
    orders: AmazonOrder[];
    nextToken?: string;
  }> {
    const queryParams: any = {
      MarketplaceIds: this.marketplaceId,
    };

    if (params.createdAfter) {
      queryParams.CreatedAfter = params.createdAfter.toISOString();
    }
    if (params.createdBefore) {
      queryParams.CreatedBefore = params.createdBefore.toISOString();
    }
    if (params.lastUpdatedAfter) {
      queryParams.LastUpdatedAfter = params.lastUpdatedAfter.toISOString();
    }
    if (params.lastUpdatedBefore) {
      queryParams.LastUpdatedBefore = params.lastUpdatedBefore.toISOString();
    }
    if (params.orderStatuses?.length) {
      queryParams.OrderStatuses = params.orderStatuses.join(',');
    }
    if (params.fulfillmentChannels?.length) {
      queryParams.FulfillmentChannels = params.fulfillmentChannels.join(',');
    }
    if (params.paymentMethods?.length) {
      queryParams.PaymentMethods = params.paymentMethods.join(',');
    }
    if (params.buyerEmail) {
      queryParams.BuyerEmail = params.buyerEmail;
    }
    if (params.sellerOrderId) {
      queryParams.SellerOrderId = params.sellerOrderId;
    }
    if (params.maxResultsPerPage) {
      queryParams.MaxResultsPerPage = params.maxResultsPerPage;
    }
    if (params.nextToken) {
      queryParams.NextToken = params.nextToken;
    }

    const response = await this.get('/orders/v0/orders', {
      params: queryParams,
    });

    return {
      orders: response.data.payload?.Orders || [],
      nextToken: response.data.payload?.NextToken,
    };
  }

  /**
   * Get order items for a specific order
   */
  async getOrderItems(orderId: string): Promise<AmazonOrderItem[]> {
    const response = await this.get(`/orders/v0/orders/${orderId}/orderItems`);
    return response.data.payload?.OrderItems || [];
  }

  /**
   * Get inventory summary
   */
  async getInventorySummary(
    params: {
      granularityType?: 'Marketplace';
      granularityId?: string;
      marketplaceIds?: string[];
      details?: boolean;
      startDateTime?: Date;
      sellerSkus?: string[];
      nextToken?: string;
      maxResultsPerPage?: number;
    } = {},
  ): Promise<{
    inventoryItems: AmazonInventoryItem[];
    nextToken?: string;
  }> {
    const queryParams: any = {
      granularityType: params.granularityType || 'Marketplace',
      granularityId: params.granularityId || this.marketplaceId,
      marketplaceIds: params.marketplaceIds?.join(',') || this.marketplaceId,
    };

    if (params.details !== undefined) {
      queryParams.details = params.details;
    }
    if (params.startDateTime) {
      queryParams.startDateTime = params.startDateTime.toISOString();
    }
    if (params.sellerSkus?.length) {
      queryParams.sellerSkus = params.sellerSkus.join(',');
    }
    if (params.nextToken) {
      queryParams.nextToken = params.nextToken;
    }
    if (params.maxResultsPerPage) {
      queryParams.maxResultsPerPage = params.maxResultsPerPage;
    }

    const response = await this.get('/fba/inventory/v1/summaries', {
      params: queryParams,
    });

    return {
      inventoryItems: response.data.payload?.inventorySummaries || [],
      nextToken: response.data.payload?.pagination?.nextToken,
    };
  }

  /**
   * Update inventory quantity
   */
  async updateInventoryQuantity(
    sellerSku: string,
    quantity: number,
    fulfillmentCenterId?: string,
  ): Promise<boolean> {
    try {
      const requestBody = {
        inventoryDetails: [
          {
            sellerSku,
            fulfillmentCenterId: fulfillmentCenterId || 'DEFAULT',
            quantity,
          },
        ],
      };

      await this.patch('/fba/inventory/v1', requestBody);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update inventory for SKU ${sellerSku}:`, error);
      return false;
    }
  }

  /**
   * Get product catalog information
   */
  async getProductCatalog(params: {
    asin?: string;
    sellerSku?: string;
    marketplaceIds?: string[];
    includedData?: string[];
  }): Promise<any> {
    const queryParams: any = {
      marketplaceIds: params.marketplaceIds?.join(',') || this.marketplaceId,
    };

    if (params.asin) {
      queryParams.asin = params.asin;
    }
    if (params.sellerSku) {
      queryParams.sellerSku = params.sellerSku;
    }
    if (params.includedData?.length) {
      queryParams.includedData = params.includedData.join(',');
    }

    const response = await this.get('/catalog/2022-04-01/items', {
      params: queryParams,
    });

    return response.data.payload?.items || [];
  }

  /**
   * Get marketplace participation
   */
  async getMarketplaceParticipations(): Promise<any> {
    const response = await this.get('/sellers/v1/marketplaceParticipations');
    return response.data.payload || [];
  }

  /**
   * Set access token for API calls
   */
  setAccessToken(token: string): void {
    this.setHeader('x-amz-access-token', token);
  }

  /**
   * Remove access token
   */
  removeAccessToken(): void {
    this.removeHeader('x-amz-access-token');
  }

  /**
   * Store new OAuth tokens (for manual token updates)
   */
  async storeTokens(tokenData: {
    accessToken: string;
    refreshToken?: string;
    expiresAt?: Date;
    scopes?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.tokenManager.storeTokens(TokenProvider.AMAZON, {
      ...tokenData,
      refreshToken: tokenData.refreshToken || '',
      shopId: this.sellerId,
      shopName: this.sellerId,
    });

    // Update current authentication
    await this.setupAuthentication();
  }

  /**
   * Get token status
   */
  async getTokenStatus(): Promise<{
    hasToken: boolean;
    isValid: boolean;
    expiresAt?: Date;
    lastUsedAt?: Date;
    refreshFailureCount: number;
  }> {
    return this.tokenManager.getTokenStatus(TokenProvider.AMAZON, this.sellerId);
  }

  /**
   * Revoke stored tokens
   */
  async revokeTokens(): Promise<void> {
    await this.tokenManager.revokeTokens(TokenProvider.AMAZON, this.sellerId);
    this.removeAccessToken();
  }
}
