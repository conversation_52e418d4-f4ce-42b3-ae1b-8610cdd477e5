import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axiosRetry from 'axios-retry';

export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  headers?: Record<string, string>;
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: any;
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  response?: any;
}

@Injectable()
export abstract class BaseApiService {
  protected readonly logger = new Logger(this.constructor.name);
  protected readonly httpClient: AxiosInstance;

  constructor(config: ApiConfig) {
    this.httpClient = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'knot-core-refactored/2.0.0',
        ...config.headers,
      },
    });

    // Configure retry logic
    axiosRetry(this.httpClient, {
      retries: config.retries || 3,
      retryDelay: retryCount => {
        const delay = config.retryDelay || 1000;
        return delay * Math.pow(2, retryCount - 1); // Exponential backoff
      },
      retryCondition: error => {
        return (
          axiosRetry.isNetworkOrIdempotentRequestError(error) ||
          (error.response?.status ? error.response.status >= 500 : false)
        );
      },
    });

    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      config => {
        this.logger.debug(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
        return config;
      },
      error => {
        this.logger.error('Request interceptor error:', error);
        return Promise.reject(error);
      },
    );

    // Response interceptor for logging and error handling
    this.httpClient.interceptors.response.use(
      response => {
        this.logger.debug(`Received ${response.status} response from ${response.config.url}`);
        return response;
      },
      error => {
        this.logger.error(`API Error: ${error.message}`, {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          data: error.response?.data,
        });
        return Promise.reject(this.transformError(error));
      },
    );
  }

  /**
   * Make GET request
   */
  protected async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.get<T>(url, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'GET', url);
    }
  }

  /**
   * Make POST request
   */
  protected async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.post<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'POST', url);
    }
  }

  /**
   * Make PUT request
   */
  protected async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.put<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'PUT', url);
    }
  }

  /**
   * Make PATCH request
   */
  protected async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.patch<T>(url, data, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'PATCH', url);
    }
  }

  /**
   * Make DELETE request
   */
  protected async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig,
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.httpClient.delete<T>(url, config);
      return this.transformResponse(response);
    } catch (error) {
      throw this.handleError(error, 'DELETE', url);
    }
  }

  /**
   * Transform axios response to our format
   */
  private transformResponse<T>(response: AxiosResponse<T>): ApiResponse<T> {
    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
  }

  /**
   * Transform axios error to our format
   */
  private transformError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        message: error.response.data?.message || error.message,
        status: error.response.status,
        code: error.response.data?.code || error.code,
        response: error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        message: 'Network error - no response received',
        code: 'NETWORK_ERROR',
      };
    } else {
      // Something else happened
      return {
        message: error.message,
        code: 'UNKNOWN_ERROR',
      };
    }
  }

  /**
   * Handle and log errors
   */
  private handleError(error: any, method: string, url: string): ApiError {
    const apiError = this.transformError(error);

    this.logger.error(`${method} ${url} failed:`, {
      message: apiError.message,
      status: apiError.status,
      code: apiError.code,
    });

    return apiError;
  }

  /**
   * Set authorization header
   */
  protected setAuthHeader(token: string, type: string = 'Bearer'): void {
    this.httpClient.defaults.headers.common['Authorization'] = `${type} ${token}`;
  }

  /**
   * Remove authorization header
   */
  protected removeAuthHeader(): void {
    delete this.httpClient.defaults.headers.common['Authorization'];
  }

  /**
   * Set custom header
   */
  protected setHeader(key: string, value: string): void {
    this.httpClient.defaults.headers.common[key] = value;
  }

  /**
   * Remove custom header
   */
  protected removeHeader(key: string): void {
    delete this.httpClient.defaults.headers.common[key];
  }

  /**
   * Get current headers
   */
  protected getHeaders(): Record<string, string> {
    return { ...this.httpClient.defaults.headers.common } as any;
  }

  /**
   * Health check method - should be implemented by subclasses
   */
  abstract healthCheck(): Promise<boolean>;

  /**
   * Rate limit check - should be implemented by subclasses if needed
   */
  protected async checkRateLimit(): Promise<void> {
    // Default implementation - no rate limiting
  }
}
