import { Injectable } from '@nestjs/common';
import { Repository, FindOptionsWhere, FindManyOptions, EntityTarget } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

import { BaseEntity } from '@domain/common/base.entity';
import {
  IRepository,
  FindOptions,
  PaginationOptions,
  PaginationResult,
} from '@domain/common/repository.interface';

@Injectable()
export abstract class BaseRepository<T extends BaseEntity> implements IRepository<T> {
  protected repository: Repository<T>;

  constructor(
    @InjectDataSource() protected dataSource: DataSource,
    private entityTarget: EntityTarget<T>,
  ) {
    this.repository = this.dataSource.getRepository(entityTarget);
  }

  async findById(id: string): Promise<T | null> {
    return this.repository.findOne({
      where: { id } as FindOptionsWhere<T>,
    });
  }

  async find(options?: FindOptions): Promise<T[]> {
    const findOptions: FindManyOptions<T> = {};

    if (options?.where) {
      findOptions.where = options.where as FindOptionsWhere<T>;
    }

    if (options?.relations) {
      findOptions.relations = options.relations;
    }

    if (options?.order) {
      findOptions.order = options.order as any;
    }

    if (options?.skip !== undefined) {
      findOptions.skip = options.skip;
    }

    if (options?.take !== undefined) {
      findOptions.take = options.take;
    }

    return this.repository.find(findOptions);
  }

  async findOne(options: FindOptions): Promise<T | null> {
    const findOptions: FindManyOptions<T> = {};

    if (options.where) {
      findOptions.where = options.where as FindOptionsWhere<T>;
    }

    if (options.relations) {
      findOptions.relations = options.relations;
    }

    return this.repository.findOne(findOptions);
  }

  async findWithPagination(options: PaginationOptions & FindOptions): Promise<PaginationResult<T>> {
    const { page, limit, orderBy, order, ...findOptions } = options;

    const skip = (page - 1) * limit;
    const take = limit;

    const queryOptions: FindManyOptions<T> = {
      skip,
      take,
    };

    if (findOptions.where) {
      queryOptions.where = findOptions.where as FindOptionsWhere<T>;
    }

    if (findOptions.relations) {
      queryOptions.relations = findOptions.relations;
    }

    if (orderBy && order) {
      queryOptions.order = { [orderBy]: order } as any;
    }

    const [data, total] = await this.repository.findAndCount(queryOptions);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  async count(where?: Record<string, any>): Promise<number> {
    return this.repository.count({
      where: where as FindOptionsWhere<T>,
    });
  }

  async exists(where: Record<string, any>): Promise<boolean> {
    const count = await this.count(where);
    return count > 0;
  }

  async save(entity: T): Promise<T> {
    return this.repository.save(entity);
  }

  async saveMany(entities: T[]): Promise<T[]> {
    return this.repository.save(entities);
  }

  create(data: Partial<T>): T {
    return this.repository.create(data as any) as unknown as T;
  }

  async update(id: string, data: Partial<T>): Promise<T | null> {
    await this.repository.update(id, data as any);
    return this.findById(id);
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.repository.delete(id);
    return (result.affected ?? 0) > 0;
  }

  async softDelete(id: string): Promise<boolean> {
    const result = await this.repository.softDelete(id);
    return (result.affected ?? 0) > 0;
  }

  async restore(id: string): Promise<boolean> {
    const result = await this.repository.restore(id);
    return (result.affected ?? 0) > 0;
  }

  async deleteMany(where: Record<string, any>): Promise<number> {
    const result = await this.repository.delete(where as FindOptionsWhere<T>);
    return result.affected ?? 0;
  }

  async query(sql: string, parameters?: any[]): Promise<any> {
    return this.repository.query(sql, parameters);
  }

  async transaction<R>(fn: (repository: IRepository<T>) => Promise<R>): Promise<R> {
    return this.dataSource.transaction(async manager => {
      const transactionalRepository = manager.getRepository(this.entityTarget);

      // Create a temporary repository wrapper for the transaction
      const transactionalWrapper: IRepository<T> = {
        findById: (id: string) =>
          transactionalRepository.findOne({ where: { id } as FindOptionsWhere<T> }),
        find: (options?: FindOptions) => {
          const findOptions: FindManyOptions<T> = {};
          if (options?.where) findOptions.where = options.where as FindOptionsWhere<T>;
          if (options?.relations) findOptions.relations = options.relations;
          if (options?.order) findOptions.order = options.order as any;
          if (options?.skip !== undefined) findOptions.skip = options.skip;
          if (options?.take !== undefined) findOptions.take = options.take;
          return transactionalRepository.find(findOptions);
        },
        findOne: (options: FindOptions) => {
          const findOptions: FindManyOptions<T> = {};
          if (options.where) findOptions.where = options.where as FindOptionsWhere<T>;
          if (options.relations) findOptions.relations = options.relations;
          return transactionalRepository.findOne(findOptions);
        },
        findWithPagination: async (options: PaginationOptions & FindOptions) => {
          const { page, limit, orderBy, order, ...findOptions } = options;
          const skip = (page - 1) * limit;
          const take = limit;

          const queryOptions: FindManyOptions<T> = { skip, take };
          if (findOptions.where) queryOptions.where = findOptions.where as FindOptionsWhere<T>;
          if (findOptions.relations) queryOptions.relations = findOptions.relations;
          if (orderBy && order) queryOptions.order = { [orderBy]: order } as any;

          const [data, total] = await transactionalRepository.findAndCount(queryOptions);
          const totalPages = Math.ceil(total / limit);

          return {
            data,
            total,
            page,
            limit,
            totalPages,
            hasNext: page < totalPages,
            hasPrevious: page > 1,
          };
        },
        count: (where?: Record<string, any>) =>
          transactionalRepository.count({ where: where as FindOptionsWhere<T> }),
        exists: async (where: Record<string, any>) => {
          const count = await transactionalRepository.count({
            where: where as FindOptionsWhere<T>,
          });
          return count > 0;
        },
        save: (entity: T) => transactionalRepository.save(entity),
        saveMany: (entities: T[]) => transactionalRepository.save(entities),
        create: (data: Partial<T>) => transactionalRepository.create(data as any) as unknown as T,
        update: async (id: string, data: Partial<T>) => {
          await transactionalRepository.update(id, data as any);
          return transactionalRepository.findOne({ where: { id } as FindOptionsWhere<T> });
        },
        delete: async (id: string) => {
          const result = await transactionalRepository.delete(id);
          return (result.affected ?? 0) > 0;
        },
        softDelete: async (id: string) => {
          const result = await transactionalRepository.softDelete(id);
          return (result.affected ?? 0) > 0;
        },
        restore: async (id: string) => {
          const result = await transactionalRepository.restore(id);
          return (result.affected ?? 0) > 0;
        },
        deleteMany: async (where: Record<string, any>) => {
          const result = await transactionalRepository.delete(where as FindOptionsWhere<T>);
          return result.affected ?? 0;
        },
        query: (sql: string, parameters?: any[]) => transactionalRepository.query(sql, parameters),
        transaction: () => {
          throw new Error('Nested transactions are not supported');
        },
      };

      return fn(transactionalWrapper);
    });
  }
}
