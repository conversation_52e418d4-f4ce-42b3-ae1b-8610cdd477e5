import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, ILike, LessThanOr<PERSON><PERSON>l, <PERSON><PERSON><PERSON>, <PERSON>Null, Not } from 'typeorm';

import { Product } from '@domain/product/product.entity';
import { ProductStatus } from '@domain/product/product-status.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { BaseRepository } from './base.repository';
import { PaginationOptions, PaginationResult } from '@domain/common/repository.interface';

export interface ProductSearchOptions {
  sku?: string;
  title?: string;
  style?: string;
  color?: string;
  design?: string;
  size?: number;
  status?: ProductStatus;
  source?: PlatformSource;
  isOutOfStock?: boolean;
  isLowStock?: boolean;
  hasCompleteInfo?: boolean;
  search?: string; // Search in SKU, title, style, color
}

@Injectable()
export class ProductRepository extends BaseRepository<Product> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(dataSource, Product);
  }

  /**
   * Find product by SKU
   */
  async findBySku(sku: string): Promise<Product | null> {
    return this.repository.findOne({
      where: { sku: sku.toUpperCase() },
      relations: ['designs'],
    });
  }

  /**
   * Find products by external product ID and source
   */
  async findByExternalId(externalProductId: string, source: PlatformSource): Promise<Product[]> {
    return this.repository.find({
      where: { 
        externalProductId,
        source,
      },
      relations: ['designs'],
    });
  }

  /**
   * Search products with filters and pagination
   */
  async searchProducts(
    searchOptions: ProductSearchOptions,
    paginationOptions: PaginationOptions,
  ): Promise<PaginationResult<Product>> {
    const queryBuilder = this.repository.createQueryBuilder('product');

    // Apply filters
    if (searchOptions.sku) {
      queryBuilder.andWhere('product.sku ILIKE :sku', { 
        sku: `%${searchOptions.sku.toUpperCase()}%` 
      });
    }

    if (searchOptions.title) {
      queryBuilder.andWhere('product.title ILIKE :title', { 
        title: `%${searchOptions.title}%` 
      });
    }

    if (searchOptions.style) {
      queryBuilder.andWhere('product.style ILIKE :style', { 
        style: `%${searchOptions.style}%` 
      });
    }

    if (searchOptions.color) {
      queryBuilder.andWhere('product.color ILIKE :color', { 
        color: `%${searchOptions.color}%` 
      });
    }

    if (searchOptions.design) {
      queryBuilder.andWhere('product.design ILIKE :design', { 
        design: `%${searchOptions.design}%` 
      });
    }

    if (searchOptions.size) {
      queryBuilder.andWhere('product.size = :size', { size: searchOptions.size });
    }

    if (searchOptions.status) {
      queryBuilder.andWhere('product.status = :status', { status: searchOptions.status });
    }

    if (searchOptions.source) {
      queryBuilder.andWhere('product.source = :source', { source: searchOptions.source });
    }

    if (searchOptions.isOutOfStock) {
      queryBuilder.andWhere(
        '(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) <= 0'
      );
    }

    if (searchOptions.isLowStock) {
      queryBuilder.andWhere(
        '(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) > 0 AND (product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) < 10'
      );
    }

    if (searchOptions.hasCompleteInfo) {
      queryBuilder.andWhere('product.style IS NOT NULL')
        .andWhere('product.color IS NOT NULL')
        .andWhere('product.size IS NOT NULL')
        .andWhere('product.size > 0');
    }

    if (searchOptions.search) {
      queryBuilder.andWhere(
        '(product.sku ILIKE :search OR product.title ILIKE :search OR product.style ILIKE :search OR product.color ILIKE :search)',
        { search: `%${searchOptions.search}%` }
      );
    }

    // Apply pagination
    const { page, limit, orderBy = 'createdAt', order = 'DESC' } = paginationOptions;
    const skip = (page - 1) * limit;

    queryBuilder
      .orderBy(`product.${orderBy}`, order)
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Find products by style, color, and size
   */
  async findByProductInfo(style: string, color: string, size: number): Promise<Product[]> {
    return this.repository.find({
      where: {
        style: ILike(style),
        color: ILike(color),
        size,
      },
      relations: ['designs'],
    });
  }

  /**
   * Find out of stock products
   */
  async findOutOfStockProducts(): Promise<Product[]> {
    return this.repository
      .createQueryBuilder('product')
      .where('(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) <= 0')
      .andWhere('product.status = :status', { status: ProductStatus.ACTIVE })
      .getMany();
  }

  /**
   * Find low stock products
   */
  async findLowStockProducts(threshold: number = 10): Promise<Product[]> {
    return this.repository
      .createQueryBuilder('product')
      .where('(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) > 0')
      .andWhere(`(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) < :threshold`, { threshold })
      .andWhere('product.status = :status', { status: ProductStatus.ACTIVE })
      .getMany();
  }

  /**
   * Find products with out of stock date older than specified days
   */
  async findProductsOutOfStockSince(days: number): Promise<Product[]> {
    const date = new Date();
    date.setDate(date.getDate() - days);

    return this.repository.find({
      where: {
        oosDate: LessThanOrEqual(date),
      },
    });
  }

  /**
   * Find products by platform source
   */
  async findByPlatformSource(source: PlatformSource): Promise<Product[]> {
    return this.repository.find({
      where: { source },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find products with incomplete information
   */
  async findProductsWithIncompleteInfo(): Promise<Product[]> {
    return this.repository
      .createQueryBuilder('product')
      .where('product.style IS NULL OR product.color IS NULL OR product.size IS NULL OR product.size <= 0')
      .getMany();
  }

  /**
   * Update quantities for platform
   */
  async updatePlatformQuantity(
    productId: string, 
    platform: PlatformSource, 
    quantity: number
  ): Promise<void> {
    const updateData: any = {};
    
    switch (platform) {
      case PlatformSource.AMAZON:
        updateData.amazonQuantity = quantity;
        updateData.amazonUpdatedTime = new Date();
        break;
      case PlatformSource.SHOPIFY:
        updateData.shopifyQuantity = quantity;
        break;
      case PlatformSource.ETSY:
        updateData.etsyQuantity = quantity;
        break;
      default:
        updateData.quantity = quantity;
    }

    await this.repository.update(productId, updateData);
  }

  /**
   * Mark products as checked
   */
  async markAsChecked(productIds: string[]): Promise<void> {
    await this.repository.update(productIds, {
      checked: true,
      checkingTime: new Date(),
    });
  }

  /**
   * Reset checked status for all products
   */
  async resetCheckedStatus(): Promise<void> {
    await this.repository.update({}, {
      checked: false,
      checkingTime: null,
    });
  }

  /**
   * Update out of stock dates
   */
  async updateOutOfStockDates(): Promise<void> {
    const currentDate = new Date();

    // Set OOS date for products that are out of stock but don't have OOS date
    await this.repository
      .createQueryBuilder()
      .update(Product)
      .set({ oosDate: currentDate })
      .where('(quantity + amazonQuantity + shopifyQuantity + etsyQuantity) <= 0')
      .andWhere('oosDate IS NULL')
      .execute();

    // Clear OOS date for products that are back in stock
    await this.repository
      .createQueryBuilder()
      .update(Product)
      .set({ oosDate: null })
      .where('(quantity + amazonQuantity + shopifyQuantity + etsyQuantity) > 0')
      .andWhere('oosDate IS NOT NULL')
      .execute();
  }

  /**
   * Delete unused products (with default title and from Shopify)
   */
  async deleteUnusedProducts(): Promise<number> {
    const result = await this.repository.delete({
      title: ILike('-'),
      source: PlatformSource.SHOPIFY,
    });

    return result.affected ?? 0;
  }

  /**
   * Get product statistics
   */
  async getProductStatistics(): Promise<{
    total: number;
    active: number;
    outOfStock: number;
    lowStock: number;
    byPlatform: Record<PlatformSource, number>;
    byStatus: Record<ProductStatus, number>;
  }> {
    const [
      total,
      active,
      outOfStock,
      lowStock,
    ] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { status: ProductStatus.ACTIVE } }),
      this.repository
        .createQueryBuilder('product')
        .where('(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) <= 0')
        .getCount(),
      this.repository
        .createQueryBuilder('product')
        .where('(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) > 0')
        .andWhere('(product.quantity + product.amazonQuantity + product.shopifyQuantity + product.etsyQuantity) < 10')
        .getCount(),
    ]);

    // Get counts by platform
    const byPlatform: Record<PlatformSource, number> = {} as any;
    for (const platform of Object.values(PlatformSource)) {
      byPlatform[platform] = await this.repository.count({ where: { source: platform } });
    }

    // Get counts by status
    const byStatus: Record<ProductStatus, number> = {} as any;
    for (const status of Object.values(ProductStatus)) {
      byStatus[status] = await this.repository.count({ where: { status } });
    }

    return {
      total,
      active,
      outOfStock,
      lowStock,
      byPlatform,
      byStatus,
    };
  }

  /**
   * Check if SKU exists
   */
  async skuExists(sku: string, excludeProductId?: string): Promise<boolean> {
    const queryBuilder = this.repository
      .createQueryBuilder('product')
      .where('UPPER(product.sku) = UPPER(:sku)', { sku });

    if (excludeProductId) {
      queryBuilder.andWhere('product.id != :excludeProductId', { excludeProductId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }
}
