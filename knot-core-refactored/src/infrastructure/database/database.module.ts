import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { User } from '@domain/user/user.entity';
import { Product } from '@domain/product/product.entity';
import { ProductDesign } from '@domain/product/product-design.entity';
import { Order } from '@domain/order/order.entity';
import { OrderItem } from '@domain/order/order-item.entity';
import { OAuthToken } from '@domain/common/entities/oauth-token.entity';
import { OrderItemRepository } from './repositories/order-item.repository';
import { OrderRepository } from './repositories/order.repository';
import { ProductDesignRepository } from './repositories/product-design.repository';
import { ProductRepository } from './repositories/product.repository';
import { UserRepository } from './repositories/user.repository';
import { OAuthTokenRepository } from './repositories/oauth-token.repository';

const entities = [User, Product, ProductDesign, Order, OrderItem, OAuthToken];

const repositories = [
  UserRepository,
  ProductRepository,
  ProductDesignRepository,
  OrderRepository,
  OrderItemRepository,
  OAuthTokenRepository,
];

@Module({
  imports: [TypeOrmModule.forFeature(entities)],
  providers: repositories,
  exports: repositories,
})
export class DatabaseModule {}
