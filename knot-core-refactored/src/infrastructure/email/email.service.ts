import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Resend } from 'resend';
import { EmailConfig } from '@shared/config/email.config';

export interface EmailTemplate {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
}

export interface VerificationEmailData {
  firstName: string;
  verificationUrl: string;
}

export interface PasswordResetEmailData {
  firstName: string;
  resetUrl: string;
}

export interface WelcomeEmailData {
  firstName: string;
  loginUrl: string;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private readonly resend: Resend;
  private readonly emailConfig: EmailConfig;

  constructor(private readonly configService: ConfigService) {
    this.emailConfig = this.configService.get<EmailConfig>('email')!;
    this.resend = new Resend(this.emailConfig.apiKey);
  }

  async sendEmail(template: EmailTemplate): Promise<boolean> {
    try {
      const result = await this.resend.emails.send({
        from: `${this.emailConfig.fromName} <${this.emailConfig.from}>`,
        to: template.to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      if (result.error) {
        this.logger.error('Failed to send email:', result.error);
        return false;
      }

      this.logger.log(`Email sent successfully: ${result.data?.id}`);
      return true;
    } catch (error) {
      this.logger.error('Error sending email:', error);
      return false;
    }
  }

  async sendVerificationEmail(email: string, data: VerificationEmailData): Promise<boolean> {
    const template: EmailTemplate = {
      to: email,
      subject: 'Verify Your Email Address - Knot Theory Rings',
      html: this.generateVerificationEmailHtml(data),
      text: this.generateVerificationEmailText(data),
    };

    return this.sendEmail(template);
  }

  async sendPasswordResetEmail(email: string, data: PasswordResetEmailData): Promise<boolean> {
    const template: EmailTemplate = {
      to: email,
      subject: 'Reset Your Password - Knot Theory Rings',
      html: this.generatePasswordResetEmailHtml(data),
      text: this.generatePasswordResetEmailText(data),
    };

    return this.sendEmail(template);
  }

  async sendWelcomeEmail(email: string, data: WelcomeEmailData): Promise<boolean> {
    const template: EmailTemplate = {
      to: email,
      subject: 'Welcome to Knot Theory Rings!',
      html: this.generateWelcomeEmailHtml(data),
      text: this.generateWelcomeEmailText(data),
    };

    return this.sendEmail(template);
  }

  private generateVerificationEmailHtml(data: VerificationEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #3b82f6; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px 20px; }
            .button { 
              display: inline-block; 
              background: #3b82f6; 
              color: white; 
              padding: 12px 30px; 
              text-decoration: none; 
              border-radius: 5px; 
              margin: 20px 0; 
            }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Knot Theory Rings</h1>
            </div>
            <div class="content">
              <h2>Hi ${data.firstName}!</h2>
              <p>Thank you for signing up with Knot Theory Rings. To complete your registration, please verify your email address by clicking the button below:</p>
              <div style="text-align: center;">
                <a href="${data.verificationUrl}" class="button">Verify Email Address</a>
              </div>
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #3b82f6;">${data.verificationUrl}</p>
              <p>This verification link will expire in 24 hours for security reasons.</p>
              <p>If you didn't create an account with us, please ignore this email.</p>
            </div>
            <div class="footer">
              <p>&copy; 2025 Knot Theory Rings. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private generateVerificationEmailText(data: VerificationEmailData): string {
    return `
Hi ${data.firstName}!

Thank you for signing up with Knot Theory Rings. To complete your registration, please verify your email address by visiting this link:

${data.verificationUrl}

This verification link will expire in 24 hours for security reasons.

If you didn't create an account with us, please ignore this email.

Best regards,
Knot Theory Rings Team
    `.trim();
  }

  private generatePasswordResetEmailHtml(data: PasswordResetEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px 20px; }
            .button { 
              display: inline-block; 
              background: #dc2626; 
              color: white; 
              padding: 12px 30px; 
              text-decoration: none; 
              border-radius: 5px; 
              margin: 20px 0; 
            }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Password Reset</h1>
            </div>
            <div class="content">
              <h2>Hi ${data.firstName}!</h2>
              <p>We received a request to reset your password for your Knot Theory Rings account. Click the button below to reset your password:</p>
              <div style="text-align: center;">
                <a href="${data.resetUrl}" class="button">Reset Password</a>
              </div>
              <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
              <p style="word-break: break-all; color: #dc2626;">${data.resetUrl}</p>
              <p>This password reset link will expire in 1 hour for security reasons.</p>
              <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
            </div>
            <div class="footer">
              <p>&copy; 2025 Knot Theory Rings. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private generatePasswordResetEmailText(data: PasswordResetEmailData): string {
    return `
Hi ${data.firstName}!

We received a request to reset your password for your Knot Theory Rings account. Visit this link to reset your password:

${data.resetUrl}

This password reset link will expire in 1 hour for security reasons.

If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

Best regards,
Knot Theory Rings Team
    `.trim();
  }

  private generateWelcomeEmailHtml(data: WelcomeEmailData): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Knot Theory Rings</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #059669; color: white; padding: 20px; text-align: center; }
            .content { padding: 30px 20px; }
            .button { 
              display: inline-block; 
              background: #059669; 
              color: white; 
              padding: 12px 30px; 
              text-decoration: none; 
              border-radius: 5px; 
              margin: 20px 0; 
            }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Knot Theory Rings!</h1>
            </div>
            <div class="content">
              <h2>Hi ${data.firstName}!</h2>
              <p>Welcome to Knot Theory Rings! Your email has been verified and your account is now active.</p>
              <p>You can now access your account and start exploring our beautiful collection of rings.</p>
              <div style="text-align: center;">
                <a href="${data.loginUrl}" class="button">Access Your Account</a>
              </div>
              <p>Thank you for joining our community!</p>
            </div>
            <div class="footer">
              <p>&copy; 2025 Knot Theory Rings. All rights reserved.</p>
            </div>
          </div>
        </body>
      </html>
    `;
  }

  private generateWelcomeEmailText(data: WelcomeEmailData): string {
    return `
Hi ${data.firstName}!

Welcome to Knot Theory Rings! Your email has been verified and your account is now active.

You can now access your account and start exploring our beautiful collection of rings.

Access your account: ${data.loginUrl}

Thank you for joining our community!

Best regards,
Knot Theory Rings Team
    `.trim();
  }
}
